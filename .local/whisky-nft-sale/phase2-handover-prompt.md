# Phase 2 Implementation Prompt - Sailing Whisky NFT Sale System
## Handover from Phase 1: Data Layer Foundation

**Project**: Sailing Whisky NFT Sale System  
**Current Status**: Phase 1 (Data Layer Foundation) ✅ COMPLETED  
**Next Phase**: Phase 2 (Core Backend Services)  
**Implementation Approach**: Sequential phases with explicit approval between phases  

---

## 🎯 **PHASE 2 IMPLEMENTATION REQUEST**

Please implement **Phase 2: Core Backend Services** for the Sailing Whisky NFT Sale system based on the completed Phase 1 foundation and following the Implementation Plan v2.

### **Implementation Rules:**
- Complete Phase 2 sequentially and wait for explicit approval before proceeding to Phase 3
- Log all actions to `.local/whisky-nft-sale/phase2-implementation.log` with format: `[TIMESTAMP] [STATUS] [MESSAGE]` where:
  - TIMESTAMP: ISO 8601 format with milliseconds
  - STATUS: (✅ Success, ❌ Fail, ⚠️ Warning, 🚫 Blocker)
  - MESSAGE: Detailed description of the action performed
- Use the existing NestJS dcasks-backend project structure and patterns
- Follow the API endpoint structure: `/api/v1/collaborations/`
- Implement proper error handling, validation, and logging

---

## 📋 **PHASE 1 COMPLETED DELIVERABLES**

### ✅ **Database Layer (COMPLETED)**
**Location**: `apps/dcasks-backend/src/nft-sale/models/`

**7 MongoDB Collections Created:**
1. **`orders`** - Central order management and lifecycle tracking
2. **`queue_sessions`** - Pool-based queue management and user session tracking  
3. **`nft_inventory`** - NFT collection and availability management
4. **`payment_records`** - Payment processing and webhook tracking
5. **`sale_config`** - System configuration and sale parameters
6. **`active_pool_tracking`** - Active pool management and statistics
7. **`admin_actions`** - Administrative actions and audit trail

**Status**: ✅ All schemas registered in NFTSaleModule, indexes created, NestJS application running successfully on http://[::1]:9000

### ✅ **Key Architecture Decisions from Phase 1:**
- **Pool-based processing** (not batch processing) - up to 100 concurrent users
- **Unified Payment Gateway** - handles both Stripe and NOWPayments
- **Order-centric workflow** - orders table as central hub
- **No order cancellation** - simplified order lifecycle
- **GraphQL + REST APIs** - following existing dcasks-backend patterns

---

## 🎯 **PHASE 2 SPECIFIC TASKS**

### **2.1 DCasks API Service Enhancement (3-4 days)**
**Location**: `apps/dcasks-backend/src/nft-sale/`

#### **Queue Management API:**
- `POST /api/v1/collaborations/queue` - Queue entry with wallet authentication
- `GET /api/v1/collaborations/queue/status` - Real-time position tracking
- Implement wallet signature validation
- Add session management with heartbeat mechanism
- Pool position calculation logic (up to 100 users)

#### **Order Management API:**
- `POST /api/v1/collaborations/orders` - Create order when NFT selected
- `GET /api/v1/collaborations/orders/:orderId` - Order details with status
- `GET /api/v1/collaborations/orders/wallet/:walletAddress` - User order history
- Order lifecycle management (created → nft_locked → payment_pending → etc.)

#### **NFT Management API:**
- `POST /api/v1/collaborations/nfts/lock` - NFT locking with 5-minute timeout
- `GET /api/v1/collaborations/nfts/available` - Available NFTs for active pool users
- `GET /api/v1/collaborations/nfts/:tokenId` - NFT details and availability
- NFT lock/unlock automation with expiration handling

#### **Core Service Logic:**
- Queue position tracking and pool management coordination
- Session state management with timeout handling
- NFT availability checking and locking mechanisms
- User authentication and authorization
- Integration with existing dcasks-backend patterns

### **2.2 DCasks Job Service Enhancement (2-3 days)**
**Location**: `apps/dcasks-backend/src/nft-sale/services/`

#### **Pool Processor:**
- Continuous pool management (up to 100 concurrent users)
- Pool activation triggers when users reach front of queue
- Pool timeout management and user expiration
- Queue position recalculation as users enter/exit

#### **Payment Monitoring Jobs:**
- Payment status monitoring for both Stripe and NOWPayments
- Payment timeout handling (5min default, 20min for NOWPayments)
- NFT unlock for failed/expired payments
- Cleanup jobs for expired sessions and orders

#### **Configuration Management:**
- Sale configuration loading from `sale_config` collection
- Dynamic configuration updates without restart
- Sale start/stop controls and pause functionality

---

## 📚 **REFERENCE DOCUMENTATION**

### **Architecture Documents:**
- **System Architecture v2**: `.local/whisky-nft-sale/system-architecture-v2.md`
- **Implementation Plan v2**: `.local/whisky-nft-sale/implementation-plan-v2.md`
- **User Stories**: `.local/whisky-nft-sale/user-stories.md`
- **Phase 1 Deliverables**: `.local/whisky-nft-sale/phase1-deliverables.md`

### **Database Schema Reference:**
```typescript
// Key Models Available (already implemented)
import {
  Order, OrderStatus, PaymentMethod,
  QueueSession, QueueSessionStatus,
  NFTInventory, NFTStatus,
  PaymentRecord, PaymentStatus, PaymentProvider,
  SaleConfig,
  ActivePoolTracking, PoolStatus,
  AdminAction, AdminActionType, TargetEntity
} from './nft-sale/models';
```

### **API Endpoint Structure:**
```typescript
// Queue Management
POST /api/v1/collaborations/queue
- Body: { walletAddress: string }
- Response: { position: number, estimatedWaitTime: number, sessionId: string, queueSessionId: string }

GET /api/v1/collaborations/queue/status
- Query: { walletAddress: string }
- Response: { position: number, status: string, poolId?: string, canSelectNFT: boolean }

// Order Management
POST /api/v1/collaborations/orders
- Body: { walletAddress: string, nftTokenId: string }
- Response: { orderId: string, status: string, expiresAt: Date }

// NFT Management  
POST /api/v1/collaborations/nfts/lock
- Body: { walletAddress: string, nftTokenId: string }
- Response: { success: boolean, lockExpiresAt: Date, orderId: string }

GET /api/v1/collaborations/nfts/available
- Response: { nfts: Array<NFT>, totalAvailable: number }
```

---

## 🔧 **TECHNICAL REQUIREMENTS**

### **Development Environment:**
- **Backend**: Node.js 22, TypeScript 5, NestJS framework (already running)
- **Database**: MongoDB with existing connection (dcasks-dev)
- **Package Manager**: pnpm
- **Existing Patterns**: Follow dcasks-backend controller/service/module structure

### **Service Communication:**
- **API Protocol**: HTTP REST with JSON payloads
- **Authentication**: Crypto wallet signatures + session tokens
- **Rate Limiting**: 100 requests/minute per wallet address
- **Database**: Use existing Mongoose connection and schemas from Phase 1

### **Error Handling:**
- **NestJS Exceptions**: Use built-in exception filters
- **Validation**: Use class-validator decorators
- **Logging**: Integrate with existing logging infrastructure
- **Retry Logic**: Implement for external service calls

---

## 📊 **PHASE 2 SUCCESS CRITERIA**

### **API Endpoints:**
✅ All queue management endpoints functional with proper validation  
✅ Order management endpoints with complete lifecycle support  
✅ NFT management endpoints with locking/unlocking automation  
✅ Proper error handling and response formatting  

### **Service Logic:**
✅ Pool-based queue processing (100 concurrent users)  
✅ Session management with heartbeat monitoring  
✅ NFT lock/unlock automation with timeout handling  
✅ Payment monitoring and cleanup jobs  

### **Integration:**
✅ Seamless integration with Phase 1 database schemas  
✅ Proper logging and error handling  
✅ Ready for Phase 3 (Payment Integration Layer)  

---

## 📝 **DELIVERABLES FOR PHASE 2**

1. **Enhanced API Service** with all queue, order, and NFT management endpoints
2. **Enhanced Job Service** with pool processing and payment monitoring
3. **Service Integration** with proper error handling and logging
4. **Comprehensive Testing** of all endpoints and service logic
5. **Implementation Log** documenting all actions and results
6. **Phase 2 Deliverables Summary** confirming readiness for Phase 3

---

## 🚀 **GETTING STARTED**

1. **Verify Phase 1 Status**: Confirm NestJS application is running with all schemas
2. **Create Phase 2 Log**: Initialize `.local/whisky-nft-sale/phase2-implementation.log`
3. **Review Architecture**: Study the system architecture v2 and implementation plan v2
4. **Start Implementation**: Begin with DCasks API Service Enhancement
5. **Follow Sequential Approach**: Complete each task before moving to the next

**Current Application Status**: ✅ Running on http://[::1]:9000 with all Phase 1 schemas active

---

**Ready to begin Phase 2: Core Backend Services implementation. Please confirm you understand the requirements and are ready to start.**
