# NFT Sale Background Jobs - Operational Guide

## Overview
This document provides comprehensive operational guidance for the two NFT Sale background jobs that manage the Sailing Whisky NFT marketplace queue system and payment timeout processing.

## Background Jobs Architecture

### Job Service Integration
All NFT Sale background jobs run within the **DCasks Job Service** (`job.module.ts`), providing:
- **Separation of Concerns**: Background processing isolated from API service
- **Independent Scaling**: Job service can scale separately from API endpoints
- **Centralized Monitoring**: All background jobs managed in one service
- **Error Isolation**: Job failures don't affect API availability

### Job Execution Environment
- **Service**: DCasks Job Service (separate from API service)
- **Startup**: `OnApplicationBootstrap` lifecycle hook
- **Error Handling**: Individual try-catch blocks with logging
- **Resource Management**: Shared MongoDB connection pool

### Payment Processing Architecture
**Background Jobs vs Webhook Handlers:**
- **Background Jobs**: Handle timeout cleanup and data maintenance (this document)
- **Webhook Handlers**: Process real-time payment status updates from gateways
- **API Endpoints**: Handle user interactions and immediate responses
- **Clear Separation**: Each component has distinct responsibilities to avoid conflicts

---

## Job 1: Pool Processor

### Job Specifications
| Property | Value |
|----------|-------|
| **Schedule** | Every 30 seconds |
| **Purpose** | Queue management and user advancement |
| **Service Method** | `poolProcessorService.processPool()` |
| **Dependencies** | MongoDB (queue_sessions, active_pool_tracking, sale_config) |
| **Average Execution Time** | 500ms - 2 seconds |
| **Resource Impact** | Medium (database-intensive) |

### Responsibilities
1. **Active Pool Management**
   - Check for heartbeat timeouts (5-minute threshold)
   - Remove completed or expired users from active pool
   - Update pool statistics and capacity tracking

2. **Queue Advancement**
   - Move waiting users from queue to active pool
   - Respect maximum active pool size limits
   - Maintain FIFO (first-in-first-out) order

3. **Session Cleanup**
   - Expire sessions with heartbeat timeouts
   - Clean up orphaned session data
   - Update session statuses appropriately

### Execution Flow
```mermaid
flowchart TD
    Start([Pool Processor Starts]) --> GetConfig[Get Sale Configuration]
    GetConfig --> CheckActive{Sale Active & Not Paused?}
    CheckActive -->|No| Skip[Skip Processing]
    CheckActive -->|Yes| ProcessActive[Process Active Pool]

    ProcessActive --> CheckSessions[Check Active Sessions]
    CheckSessions --> HeartbeatCheck{Heartbeat Timeout?}
    HeartbeatCheck -->|Yes| ExpireSession[Mark Session as EXPIRED]
    HeartbeatCheck -->|No| CheckComplete{Session Completed?}
    CheckComplete -->|Yes| RemoveFromPool[Remove from Active Pool]
    CheckComplete -->|No| NextSession[Check Next Session]

    ExpireSession --> RemoveFromPool
    RemoveFromPool --> UpdateStats[Update Pool Statistics]
    NextSession --> MoreSessions{More Sessions?}
    MoreSessions -->|Yes| CheckSessions
    MoreSessions -->|No| FillPool[Fill Pool from Queue]

    FillPool --> CheckCapacity{Pool Has Space?}
    CheckCapacity -->|No| Cleanup[Cleanup Expired Sessions]
    CheckCapacity -->|Yes| GetWaiting["Get Waiting Users - FIFO Order"]
    GetWaiting --> HasWaiting{Users Waiting?}
    HasWaiting -->|No| Cleanup
    HasWaiting -->|Yes| MoveToActive[Move Users to Active Pool]

    MoveToActive --> SetExpiration[Set Selection Expiration]
    SetExpiration --> UpdatePool[Update Pool Tracking]
    UpdatePool --> Cleanup

    Cleanup --> CleanExpired[Remove Expired Sessions]
    CleanExpired --> End([Process Complete])
    Skip --> End
```

### State Transitions Managed
- **WAITING** → **ACTIVE**: Users moved from queue to active pool
- **ACTIVE** → **EXPIRED**: Heartbeat timeout or selection timeout
- **ACTIVE** → **COMPLETED**: User completed purchase
- **SELECTING_NFT** → **EXPIRED**: Selection window timeout

### Key Database Operations
- **Heartbeat Timeout Check**: Sessions inactive for 5+ minutes are marked as expired
- **FIFO Queue Processing**: Waiting users are moved to active pool in join order
- **Pool Capacity Management**: Respects maximum active pool size limits
- **Session State Updates**: Updates session status and timestamps appropriately

---

## Job 2: Payment Monitor

### Job Specifications
| Property | Value |
|----------|-------|
| **Schedule** | Every 1 minute |
| **Purpose** | Payment timeout cleanup and expiration handling |
| **Service Method** | `paymentMonitorService.monitorPayments()` |
| **Dependencies** | MongoDB (payment_records, orders, nft_inventory) |
| **Average Execution Time** | 200ms - 500ms |
| **Resource Impact** | Low (database operations only) |

### Responsibilities
1. **Payment Timeout Detection**
   - Identify payments that have exceeded their timeout periods
   - Check payment expiration timestamps against current time
   - Flag payments for timeout processing

2. **Timeout Cleanup Operations**
   - Mark expired payments with EXPIRED status
   - Release NFT locks for timed-out payments
   - Update related order statuses to EXPIRED
   - Clean up session states for expired payments

3. **Data Consistency Maintenance**
   - Ensure NFT inventory is properly unlocked
   - Maintain referential integrity during cleanup
   - Log timeout events for monitoring and analytics

**Note**: Payment status updates (success/failure) are handled by webhook processors from payment gateways, not by this background job.

### Execution Flow
```mermaid
flowchart TD
    Start([Payment Monitor Starts]) --> GetTimedOut[Get Payments Past Timeout]
    GetTimedOut --> HasTimedOut{Timed Out Payments Found?}
    HasTimedOut -->|No| End([Monitor Complete])
    HasTimedOut -->|Yes| ProcessTimeout[Process Each Timed Out Payment]

    ProcessTimeout --> CheckPaymentType{Check Payment Type}
    CheckPaymentType -->|Stripe| CheckStripeTimeout[Verify Stripe Timeout Period]
    CheckPaymentType -->|NOWPayments| CheckNOWTimeout[Verify NOWPayments Timeout Period]
    CheckPaymentType -->|Other| CheckGenericTimeout[Verify Generic Timeout Period]

    CheckStripeTimeout --> ConfirmExpired{Confirm Payment Expired?}
    CheckNOWTimeout --> ConfirmExpired
    CheckGenericTimeout --> ConfirmExpired

    ConfirmExpired -->|No| NextPayment[Process Next Payment]
    ConfirmExpired -->|Yes| ExpirePayment[Mark Payment as EXPIRED]

    ExpirePayment --> UpdateOrder[Update Order Status to EXPIRED]
    UpdateOrder --> ReleaseNFTLock[Release NFT Lock]
    ReleaseNFTLock --> UpdateSession[Update Queue Session Status]
    UpdateSession --> LogTimeout[Log Timeout Event]
    LogTimeout --> NextPayment

    NextPayment --> MorePayments{More Payments to Process?}
    MorePayments -->|Yes| ProcessTimeout
    MorePayments -->|No| CleanupOrphaned[Cleanup Orphaned Records]
    CleanupOrphaned --> End
```

### Payment Timeout Management
- **Gateway-Specific Timeouts**: Respects different timeout periods for Stripe vs NOWPayments
- **Timeout Verification**: Confirms payments have truly exceeded their allowed time limits
- **Cleanup Operations**: Systematically releases resources for expired payments
- **Webhook Coordination**: Works alongside webhook handlers that manage payment status updates

**Important**: Payment status updates (success/failure) are handled by webhook processors, not this job.

### State Transitions Managed
- **PENDING** → **EXPIRED**: Payment timeout exceeded
- **PROCESSING** → **EXPIRED**: Payment processing timeout exceeded
- **Order Status**: PAYMENT_PENDING → EXPIRED (for related orders)
- **NFT Status**: LOCKED → AVAILABLE (release NFT locks)
- **Session Status**: SELECTING_NFT → EXPIRED (update queue sessions)

**Note**: Success/failure transitions (PENDING→SUCCEEDED, PROCESSING→FAILED) are handled by webhook processors.

---

## Monitoring & Troubleshooting

### Key Metrics & Alerts
- **Pool Processor**: Execution time (<10s), queue advancement rate, session timeout rate
- **Payment Monitor**: Timeout detection rate, cleanup efficiency (<30s execution)
- **Job Failure Rate**: <5% failures in 5-minute window

### Common Issues & Solutions

#### Pool Processor Problems
**Users stuck in queue despite available space:**
- Check sale configuration is active and not paused
- Verify active pool capacity and limits
- Monitor pool processor execution timing

**High heartbeat timeout rate:**
- Review frontend heartbeat implementation
- Check network connectivity and latency
- Consider adjusting heartbeat timeout threshold

#### Payment Monitor Problems
**Payments not expiring properly:**
- Validate timeout configurations (Stripe: 30min, NOWPayments: 20min)
- Check payment creation timestamps and calculations
- Review job execution logs for errors

**NFT locks not released after timeout:**
- Identify payments marked EXPIRED with NFTs still locked
- Manually release locks for confirmed expired payments
- Ensure referential integrity between payments, orders, and NFTs

### Performance & Recovery

#### Key Database Indexes
- Queue sessions: status + join timestamp
- Payment records: status + expiration timestamp
- Session heartbeats: last heartbeat timestamp

#### Service Recovery
- Restart job service deployment when needed
- Scale replicas for high availability
- Verify job execution resumes properly

#### Emergency Data Recovery
**Session Recovery:**
- Reset expired heartbeat sessions to waiting status
- Clear pool assignments for recovered sessions

**Payment Recovery:**
- Mark stuck payments as expired appropriately
- Reconcile status with gateway records
- Release NFT locks properly

## Configuration

### Job Parameters
**Pool Processor:**
- Interval: 30 seconds
- Heartbeat timeout: 5 minutes
- Selection timeout: 5 minutes (configurable)
- Maximum pool size: 100 users (configurable)

**Payment Monitor:**
- Interval: 1 minute
- Stripe timeout: 30 minutes
- NOWPayments timeout: 20 minutes (configurable)
- Batch size: 100 payments per cycle

### System Integration
- **Database**: Shared MongoDB connection with API service
- **Configuration**: Jobs read from sale_config collection
- **Frontend**: Polls API for updates, sends heartbeats
- **Payment Gateways**: Webhook handlers process real-time updates (not jobs)
- **Monitoring**: Jobs emit metrics for external systems
