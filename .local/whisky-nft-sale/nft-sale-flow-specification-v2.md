# NFT Sale System V2 - Complete Flow Specification

## Overview

This document defines the complete user journey and system flows for the Sailing Whisky NFT Sale System V2, including queue management, NFT selection, payment processing, and completion flows with detailed state transitions and error handling.

## System Architecture Overview

### Core Components

- **Capcom Frontend**: User interface and wallet connection
- **DCasks Backend**: Main API service with queue management
- **DCasks Job Service**: Background processing for pool management and payments
- **Payment Gateways**: Stripe (cards/digital wallets) and NOWPayments (crypto)
- **MongoDB Database**: All system data storage
- **Blockchain**: Arbitrum network for NFT transfers

## Complete User Journey Flow

### 1. Queue Joining & Management

```mermaid
sequenceDiagram
    participant User
    participant <PERSON><PERSON> as Capcom Frontend
    participant DCasks as DCasks Backend
    participant JobService as DCasks Job Service
    participant DB as MongoDB

    User->>Capcom: Connect wallet & visit landing page
    Capcom->>DCasks: POST /api/v1/collaborations/queue/join
    DCasks->>DB: Check existing session
    DCasks->>DB: Validate sale config & queue capacity
    DCasks->>DB: Create/update queue session
    DCasks-->>Capcom: Return position & session details
    Capcom->>User: Show queue position & estimated wait

    Note over JobService: Background pool processing (every 30s)
    JobService->>DB: Check active pool capacity
    JobService->>DB: Move waiting users to active pool
    JobService->>DB: Update queue sessions to ACTIVE status

    Note over User: User maintains connection
    Capcom->>DCasks: POST /api/v1/collaborations/queue/heartbeat
    DCasks->>DB: Update queueHeartbeat
    DCasks-->>Capcom: Return updated position & status
```

### 2. NFT Selection Phase

```mermaid
sequenceDiagram
    participant User
    participant Capcom as Capcom Frontend
    participant DCasks as DCasks Backend
    participant DB as MongoDB

    Note over User: User becomes ACTIVE in pool
    Capcom->>DCasks: GET /api/v1/collaborations/queue/status
    DCasks-->>Capcom: Return canSelectNFT: true
    Capcom->>User: Show NFT selection interface

    User->>Capcom: Select NFT & payment method
    Capcom->>DCasks: POST /api/v1/collaborations/orders/create
    DCasks->>DB: Lock selected NFT
    DCasks->>DB: Create order record
    DCasks->>DB: Update session to SELECTING_NFT
    DCasks-->>Capcom: Return order details
    Capcom->>User: Show order confirmation
```

### 3. Payment Processing Flow

```mermaid
sequenceDiagram
    participant User
    participant Capcom as Capcom Frontend
    participant DCasks as DCasks Backend
    participant PaymentGW as Payment Gateway
    participant JobService as DCasks Job Service
    participant DB as MongoDB

    User->>Capcom: Confirm payment
    Capcom->>DCasks: POST /api/v1/collaborations/payments/initiate
    DCasks->>PaymentGW: Create payment session
    PaymentGW-->>DCasks: Return payment URL
    DCasks->>DB: Create payment record
    DCasks-->>Capcom: Return payment URL
    Capcom->>User: Redirect to payment gateway

    User->>PaymentGW: Complete payment
    PaymentGW->>DCasks: Webhook: payment completed
    DCasks->>DB: Update payment status to SUCCEEDED
    DCasks->>DB: Update order status to PAYMENT_COMPLETED
    DCasks->>DB: Update session status to COMPLETED

    Note over JobService: Background payment monitoring (every 1min)
    JobService->>DB: Check pending payments
    JobService->>PaymentGW: Verify payment status
    JobService->>DB: Update payment records
```

## State Transition Diagrams

### Queue Session States

```mermaid
stateDiagram-v2
    [*] --> WAITING : User joins queue
    WAITING --> ACTIVE : Pool processor moves to active pool
    ACTIVE --> SELECTING_NFT : User creates order
    SELECTING_NFT --> COMPLETED : Payment successful
    SELECTING_NFT --> EXPIRED : Selection timeout
    ACTIVE --> EXPIRED : Heartbeat timeout
    WAITING --> EXPIRED : Session timeout
    WAITING --> LEFT_QUEUE : User leaves voluntarily
    ACTIVE --> LEFT_QUEUE : User leaves voluntarily
    SELECTING_NFT --> LEFT_QUEUE : User leaves voluntarily
    EXPIRED --> [*]
    COMPLETED --> [*]
    LEFT_QUEUE --> [*]
```

### Order States

```mermaid
stateDiagram-v2
    [*] --> CREATED : Order initiated
    CREATED --> NFT_LOCKED : NFT successfully locked
    NFT_LOCKED --> PAYMENT_PENDING : Payment session created
    PAYMENT_PENDING --> PAYMENT_PROCESSING : Payment in progress
    PAYMENT_PROCESSING --> PAYMENT_COMPLETED : Payment successful
    PAYMENT_PROCESSING --> PAYMENT_FAILED : Payment declined/failed
    PAYMENT_PENDING --> PAYMENT_CANCELLED : User cancelled
    NFT_LOCKED --> EXPIRED : Selection timeout
    PAYMENT_PENDING --> EXPIRED : Payment timeout
    PAYMENT_COMPLETED --> COMPLETED : NFT transferred
    PAYMENT_FAILED --> [*]
    PAYMENT_CANCELLED --> [*]
    EXPIRED --> [*]
    COMPLETED --> [*]
```

### NFT Inventory States

```mermaid
stateDiagram-v2
    [*] --> AVAILABLE : NFT ready for sale
    AVAILABLE --> LOCKED : User selects NFT
    LOCKED --> SOLD : Payment completed
    LOCKED --> AVAILABLE : Lock expires/cancelled
    SOLD --> [*]
```

### Payment Record States

```mermaid
stateDiagram-v2
    [*] --> CREATED : Payment initiated
    CREATED --> SESSION_CREATED : Gateway session created
    SESSION_CREATED --> PENDING : User redirected to gateway
    PENDING --> PROCESSING : Payment in progress
    PROCESSING --> SUCCEEDED : Payment confirmed
    PROCESSING --> FAILED : Payment declined
    PENDING --> CANCELLED : User cancelled
    SESSION_CREATED --> EXPIRED : Session timeout
    SUCCEEDED --> [*]
    FAILED --> [*]
    CANCELLED --> [*]
    EXPIRED --> [*]
```

## Time Field Organization

### Queue Session Time Fields

```typescript
// Queue-related times
queueJoinedAt: Date;           // When user joined queue
queueHeartbeat?: Date;         // Queue phase heartbeat timestamp

// Selection-related times
selectionStartedAt?: Date;     // When NFT selection began
selectionExpiresAt?: Date;     // Selection window deadline
selectionHeartbeat?: Date;     // Selection phase heartbeat

// Payment-related times
paymentStartedAt?: Date;       // When payment process began
paymentExpiresAt?: Date;       // Payment deadline
paymentCompletedAt?: Date;     // When payment finished
```

## Error Handling Scenarios

### 1. Queue Management Errors

#### Scenario: Queue Full

```
Trigger: User tries to join when queue at capacity
Flow: Join request → Queue capacity check → Rejection
Actions:
1. Return 409 Conflict with queue full message
2. Suggest user try again later
3. Log queue capacity metrics
```

#### Scenario: Heartbeat Timeout

```
Trigger: No heartbeat for 5+ minutes
Flow: ACTIVE/WAITING → EXPIRED
Actions:
1. Pool processor marks session as EXPIRED
2. Remove from active pool if applicable
3. Release any locked NFTs
4. Clean up session data
```

### 2. NFT Selection Errors

#### Scenario: NFT Already Sold

```
Trigger: User selects NFT that was just purchased
Flow: Selection attempt → NFT availability check → Rejection
Actions:
1. Return 409 Conflict with NFT unavailable message
2. Refresh available NFT list for user
3. Allow user to select different NFT
```

#### Scenario: Selection Timeout

```
Trigger: User doesn't complete selection within time limit
Flow: SELECTING_NFT → EXPIRED
Actions:
1. Release NFT lock automatically
2. Expire any pending orders
3. Remove user from active pool
4. Update session status to EXPIRED
```

### 3. Payment Processing Errors

#### Scenario: Payment Declined

```
Trigger: Payment gateway returns failure
Flow: PAYMENT_PROCESSING → PAYMENT_FAILED
Actions:
1. Update payment record status to FAILED
2. Update order status to PAYMENT_FAILED
3. Release NFT lock (make available again)
4. Allow user to retry with different payment method
```

#### Scenario: Payment Timeout

```
Trigger: Payment session expires (30 minutes)
Flow: PAYMENT_PENDING → EXPIRED
Actions:
1. Update payment record status to EXPIRED
2. Update order status to EXPIRED
3. Release NFT lock automatically
4. Remove user from active pool
```

## Background Job Processing

### Pool Processor (Every 30 seconds)

- Check active pool for timeouts and completions
- Fill available spots from waiting queue
- Clean up expired sessions
- Manage user advancement through queue system

### Payment Monitor (Every 1 minute)

- Identify payments that have exceeded timeout periods
- Mark expired payments and release associated resources
- Clean up NFT locks for timed-out payments
- Maintain data consistency during timeout processing

**Note**: Payment status updates (success/failure) are handled by webhook processors from payment gateways, not by background jobs.

## API Endpoint Mapping

### Queue Management

- `POST /api/v1/collaborations/queue/join` - Join sale queue
- `GET /api/v1/collaborations/queue/status` - Get queue position & status
- `POST /api/v1/collaborations/queue/heartbeat` - Maintain session
- `POST /api/v1/collaborations/queue/leave` - Leave queue

### Order Management

- `POST /api/v1/collaborations/orders/create` - Create NFT order
- `GET /api/v1/collaborations/orders/:orderId` - Get order details
- `POST /api/v1/collaborations/orders/:orderId/cancel` - Cancel order

### Payment Processing

- `POST /api/v1/collaborations/payments/initiate` - Start payment
- `GET /api/v1/collaborations/payments/:paymentId` - Get payment status
- `POST /api/v1/collaborations/payments/webhook` - Payment gateway webhook

### NFT Inventory

- `GET /api/v1/collaborations/nfts/available` - List available NFTs
- `GET /api/v1/collaborations/nfts/:tokenId` - Get NFT details

## Performance & Monitoring

### Response Time Targets

- **Queue Operations**: < 1 second
- **NFT Selection**: < 2 seconds
- **Payment Initiation**: < 3 seconds
- **Status Queries**: < 500ms

### Concurrency Limits

- **Active Pool Size**: 100 concurrent users
- **Total Queue Size**: 1000 users maximum
- **Payment Sessions**: 100 concurrent sessions
- **Database Connections**: 20 connection pool

### Key Metrics

- **Queue Join Success Rate**: Target 99%+
- **NFT Selection Success Rate**: Target 95%+
- **Payment Success Rate**: Target 90%+
- **Average Queue Wait Time**: Target < 10 minutes
- **Average Selection Time**: Target < 5 minutes

## Integration Points & Data Flow

### Database Collections & Relationships

```mermaid
erDiagram
    SALE_CONFIG ||--o{ ACTIVE_POOL_TRACKING : configures
    ACTIVE_POOL_TRACKING ||--o{ QUEUE_SESSIONS : manages
    QUEUE_SESSIONS ||--o{ ORDERS : creates
    ORDERS ||--o{ PAYMENT_RECORDS : triggers
    ORDERS ||--o{ NFT_INVENTORY : locks
    ADMIN_ACTIONS ||--o{ QUEUE_SESSIONS : affects
    ADMIN_ACTIONS ||--o{ ORDERS : affects

    SALE_CONFIG {
        string configVersion PK
        boolean isActive
        boolean isPaused
        number maxQueueSize
        number maxActivePoolSize
        number selectionTimeoutMinutes
        date saleStartTime
        date saleEndTime
    }

    QUEUE_SESSIONS {
        ObjectId _id PK
        string walletAddress UK
        number position
        enum status
        string poolId FK
        date queueJoinedAt
        date queueHeartbeat
        date selectionStartedAt
        date selectionExpiresAt
        date selectionHeartbeat
        date paymentStartedAt
        date paymentExpiresAt
        date paymentCompletedAt
        string sessionToken
        string currentOrderId FK
    }

    ORDERS {
        ObjectId _id PK
        string orderId UK
        string walletAddress
        string queueSessionId FK
        string nftTokenId FK
        enum status
        number nftPrice
        string currency
        string paymentMethod
        date createdAt
        date nftLockedAt
        date expiresAt
    }

    NFT_INVENTORY {
        ObjectId _id PK
        string tokenId UK
        enum status
        boolean isActive
        number price
        string currency
        string lockedBy
        string orderId FK
        date lockExpiresAt
    }

    PAYMENT_RECORDS {
        ObjectId _id PK
        string paymentId UK
        string orderId FK
        string walletAddress
        enum status
        string paymentMethod
        string paymentProvider
        number amount
        string currency
        string externalTransactionId
        date createdAt
        date expiresAt
    }
```

### Service Communication Patterns

#### 1. Queue Service → Pool Processor

```typescript
// Pool processor calls queue service methods
await this.queueService.moveToActivePool(walletAddress, poolId);
await this.queueService.leaveQueue(walletAddress);
```

#### 2. Order Service → NFT Inventory Service

```typescript
// Order service locks NFTs through inventory service
const lockedNFT = await this.nftInventoryService.lockNFT(
  tokenId,
  walletAddress
);
await this.nftInventoryService.unlockNFT(tokenId);
```

#### 3. Payment Service → Order Service

```typescript
// Payment completion triggers order updates
await this.orderService.updateOrderStatus(
  orderId,
  OrderStatus.PAYMENT_COMPLETED
);
await this.orderService.completeOrder(orderId, transactionHash);
```

### Authentication & Authorization Flow

```mermaid
sequenceDiagram
    participant User
    participant Capcom as Capcom Frontend
    participant DCasks as DCasks Backend
    participant JWT as JWT Service

    User->>Capcom: Connect wallet (MetaMask/WalletConnect)
    Capcom->>Capcom: Generate signature message
    User->>Capcom: Sign message with wallet
    Capcom->>DCasks: POST /auth/wallet-login
    DCasks->>JWT: Generate JWT token
    JWT-->>DCasks: Return signed token
    DCasks-->>Capcom: Return JWT + user session
    Capcom->>Capcom: Store JWT in secure storage

    Note over Capcom: All subsequent API calls
    Capcom->>DCasks: API calls with Authorization: Bearer <JWT>
    DCasks->>JWT: Validate token signature
    JWT-->>DCasks: Return user context
    DCasks->>DCasks: Process request with user context
```

### Error Recovery Mechanisms

#### 1. Database Connection Recovery

```typescript
// Implement retry logic with exponential backoff
async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries = 3
): Promise<T> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === maxRetries) throw error;
      await sleep(Math.pow(2, attempt) * 1000); // Exponential backoff
    }
  }
}
```

#### 2. Payment Gateway Recovery

```typescript
// Webhook retry mechanism
async function processWebhookWithRetry(webhookData: any): Promise<void> {
  const maxRetries = 5;
  const retryDelays = [1000, 2000, 5000, 10000, 30000]; // ms

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      await processPaymentWebhook(webhookData);
      return; // Success
    } catch (error) {
      if (attempt === maxRetries - 1) {
        // Final attempt failed - queue for manual review
        await queueForManualReview(webhookData, error);
        throw error;
      }
      await sleep(retryDelays[attempt]);
    }
  }
}
```

#### 3. Session Recovery

```typescript
// Recover user sessions after server restart
async function recoverActiveSessions(): Promise<void> {
  const activeSessions = await QueueSession.find({
    status: { $in: ["ACTIVE", "SELECTING_NFT"] },
    queueHeartbeat: { $gte: new Date(Date.now() - 5 * 60 * 1000) },
  });

  for (const session of activeSessions) {
    // Validate session is still valid
    if (await isSessionValid(session)) {
      // Restore to active pool
      await addToActivePool(session);
    } else {
      // Mark as expired
      await expireSession(session);
    }
  }
}
```

### Monitoring & Alerting Configuration

#### Key Performance Indicators (KPIs)

```yaml
queue_metrics:
  - queue_join_rate: "joins per minute"
  - queue_wait_time: "average wait time in minutes"
  - active_pool_utilization: "percentage of pool capacity used"
  - session_timeout_rate: "percentage of sessions timing out"

order_metrics:
  - order_creation_rate: "orders per minute"
  - order_completion_rate: "percentage of orders completed"
  - nft_selection_time: "average time to select NFT"
  - order_abandonment_rate: "percentage of orders abandoned"

payment_metrics:
  - payment_success_rate: "percentage of successful payments"
  - payment_processing_time: "average payment completion time"
  - payment_failure_reasons: "breakdown of failure types"
  - webhook_delivery_rate: "percentage of webhooks delivered"

system_metrics:
  - api_response_time: "average API response time"
  - database_query_time: "average database operation time"
  - job_processing_time: "background job execution time"
  - error_rate: "percentage of requests resulting in errors"
```

#### Alert Thresholds

```yaml
critical_alerts:
  - queue_join_failure_rate > 5%
  - payment_success_rate < 85%
  - api_response_time > 5000ms
  - database_connection_errors > 10/minute

warning_alerts:
  - queue_wait_time > 15_minutes
  - active_pool_utilization > 90%
  - payment_processing_time > 300_seconds
  - webhook_delivery_rate < 95%

info_alerts:
  - queue_size > 500_users
  - order_abandonment_rate > 20%
  - session_timeout_rate > 10%
  - job_processing_time > 60_seconds
```

This comprehensive flow specification covers all aspects of the NFT Sale System V2, providing clear guidance for implementation, testing, monitoring, and maintenance.
