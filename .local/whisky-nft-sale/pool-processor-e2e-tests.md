# Pool Processor Background Job E2E Test Cases

## Overview

This document defines comprehensive End-to-End test cases for the Pool Processor background job flow using Vitest with real MongoDB database connections. The tests focus on queue management, FIFO processing, user selection, payment timeout handling, and pool state transitions.

## Test Framework Configuration

### Vitest Setup
- **Framework**: Vitest E2E testing with real MongoDB connections
- **Environment**: SIT (System Integration Testing) environment
- **Database**: Real MongoDB database with seeding and cleanup utilities
- **Test Timeout**: 30 seconds per test
- **Server**: Real dcasks-backend server running on port 9000

### Test File Structure
```
test/e2e/pool-processor.e2e.spec.ts
```

### Database Setup Requirements
```bash
# Before tests: Seed database with test data
yarn db:seed

# After tests: Clean up database
yarn db:cleanup

# Complete workflow
yarn test:e2e:full
```

## Test Organization

Tests are organized into functional groups following the Given/When/Then pattern:

1. **Queue Management Tests** - Basic queue operations and FIFO processing
2. **Pool State Transition Tests** - User status changes and pool management
3. **Payment Timeout Handling Tests** - Payment expiration and cleanup
4. **Error Scenario Tests** - Edge cases and error conditions
5. **Performance Tests** - Load testing and concurrent operations

## Test Cases

### 1. Queue Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given queue has 5 waiting users, when pool processor runs, then first user is selected for purchase` | Verify FIFO queue processing moves first waiting user to active pool | First user status changes from WAITING to ACTIVE |
| `Given active pool is at capacity (10 users), when pool processor runs, then no new users are moved from queue` | Verify pool capacity limits are respected | Queue users remain in WAITING status |
| `Given queue has 10 users and pool has space for 5, when pool processor runs, then exactly 5 users are moved to active pool` | Verify batch processing respects available pool capacity | Exactly 5 users moved to ACTIVE status |
| `Given empty queue, when pool processor runs, then no errors occur and pool remains unchanged` | Verify graceful handling of empty queue scenarios | No errors, pool statistics unchanged |
| `Given queue with expired sessions, when pool processor runs, then expired sessions are cleaned up` | Verify automatic cleanup of expired queue sessions | Expired sessions removed from queue |

### 2. Pool State Transition Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given user in active pool with heartbeat timeout, when pool processor runs, then user is marked as expired` | Verify heartbeat timeout detection (5-minute threshold) | User status changes from ACTIVE to EXPIRED |
| `Given user completes NFT selection, when pool processor runs, then user is removed from active pool` | Verify completed users are removed from pool tracking | User removed from active pool, pool capacity updated |
| `Given user in PAYMENT status with payment timeout, when pool processor runs, then user is expired and removed` | Verify payment window timeout handling (5 minutes) | User status changes to EXPIRED, removed from pool |
| `Given multiple users with different statuses, when pool processor runs, then each user is processed according to their status` | Verify correct status-based processing logic | Each user processed correctly based on current status |
| `Given user session expires during active pool processing, when pool processor runs, then session is cleaned up and pool rebalanced` | Verify session cleanup and pool rebalancing | Session removed, pool statistics updated |

### 3. Payment Timeout Handling Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given user with payment timeout, when pool processor runs, then user is removed and next user is selected` | Verify payment timeout triggers user removal and queue advancement | Timed-out user removed, next user moved to ACTIVE |
| `Given multiple users with payment timeouts, when pool processor runs, then all timed-out users are processed` | Verify batch processing of multiple payment timeouts | All timed-out users removed, pool refilled from queue |
| `Given user payment expires during pool processing, when pool processor runs, then NFT lock is released` | Verify NFT lock release for payment timeouts | NFT status changes from LOCKED to AVAILABLE |
| `Given payment timeout with related order, when pool processor runs, then order status is updated to expired` | Verify order status updates for payment timeouts | Order status changes to EXPIRED |

### 4. Error Scenario Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given database connection error, when pool processor runs, then error is logged and processing continues` | Verify graceful error handling for database issues | Error logged, no system crash |
| `Given corrupted session data, when pool processor runs, then corrupted sessions are cleaned up` | Verify handling of invalid session data | Corrupted sessions removed, valid sessions processed |
| `Given sale configuration is paused, when pool processor runs, then no queue processing occurs` | Verify respect for sale configuration settings | No users moved, queue remains unchanged |
| `Given invalid pool tracking data, when pool processor runs, then pool tracking is reset and rebuilt` | Verify recovery from corrupted pool tracking | Pool tracking reset, rebuilt from current sessions |
| `Given concurrent pool processor executions, when multiple instances run, then no duplicate processing occurs` | Verify thread safety and concurrent execution handling | No duplicate user processing, consistent state |

### 5. Performance Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given 1000 users in queue, when pool processor runs, then processing completes within 10 seconds` | Verify performance with large queue sizes | Processing completes within time limit |
| `Given 100 active users with mixed statuses, when pool processor runs, then all users are processed efficiently` | Verify efficient processing of full active pool | All users processed within performance threshold |
| `Given rapid heartbeat updates during processing, when pool processor runs, then no race conditions occur` | Verify handling of concurrent heartbeat updates | Consistent state, no data corruption |

## Test Implementation Structure

### Test File Structure
```
test/e2e/pool-processor.e2e.spec.ts
```

### Test Organization Framework
- **Test Groups**: Organized by functional areas (Queue Management, Pool State Transitions, etc.)
- **Naming Convention**: Given/When/Then pattern for descriptive test names
- **Test Isolation**: Each test runs with fresh database state
- **Environment**: SIT environment with real MongoDB connections

### Database Seeding Requirements

For each test, the following data should be seeded:

1. **Sale Configuration**: Active sale with proper queue and pool limits
2. **Queue Sessions**: Test users in various states (WAITING, ACTIVE, PAYMENT, EXPIRED)
   - WAITING users with different heartbeat timestamps (recent, 30s ago, 1min+ ago)
   - ACTIVE users with different heartbeat timestamps (recent, 3min ago, 5min+ ago)
3. **Active Pool Tracking**: Current pool statistics and capacity tracking
4. **NFT Inventory**: Available NFTs for selection and locking
5. **Orders**: Test orders in various payment states
6. **Payment Records**: Test payments with different timeout scenarios

### Test Data Cleanup

After each test:
1. Remove all test queue sessions
2. Reset active pool tracking
3. Unlock any test NFT locks
4. Remove test orders and payment records
5. Reset sale configuration to default test state

## Integration with Existing E2E Framework

These tests will integrate with the existing E2E testing framework:

- **Database Scripts**: Use existing `yarn db:seed` and `yarn db:cleanup` commands
- **Test Environment**: Run in SIT environment with real MongoDB connections
- **Test Execution**: Use `yarn test:e2e:full` workflow for complete testing
- **Error Handling**: Follow existing error logging and reporting patterns

## Advanced Test Scenarios

### 6. Pool Capacity Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given pool at 9/10 capacity with 1 user completing, when pool processor runs, then 1 new user is added from queue` | Verify precise capacity management | Exactly 1 user moved from queue to maintain 10 capacity |
| `Given pool capacity changes during processing, when pool processor runs, then new capacity limits are respected` | Verify dynamic capacity adjustment | Pool size adjusts to new configuration limits |
| `Given pool with users in different selection stages, when pool processor runs, then capacity calculation accounts for all active users` | Verify accurate capacity calculation | Pool capacity correctly calculated including all active states |

### 7. Heartbeat and Session Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given waiting user with recent heartbeat (< 1 minute), when pool processor runs, then user remains in queue` | Verify waiting user heartbeat validation | User stays WAITING with valid heartbeat |
| `Given waiting user with heartbeat timeout (> 1 minute), when pool processor runs, then user is expired and removed from queue` | Verify waiting user heartbeat timeout | User marked as EXPIRED, queue positions updated |
| `Given active user with recent heartbeat (< 5 minutes), when pool processor runs, then user remains in active pool` | Verify active user heartbeat validation | User stays ACTIVE with valid heartbeat |
| `Given active user with heartbeat exactly at 5-minute threshold, when pool processor runs, then user is marked as expired` | Verify precise timeout boundary handling | User marked as EXPIRED at exact threshold |
| `Given multiple waiting users with staggered heartbeat timeouts, when pool processor runs, then users are expired in correct order` | Verify batch timeout processing for waiting users | Users expired based on individual timeout times, queue reordered |
| `Given mixed waiting and active users with heartbeat timeouts, when pool processor runs, then different timeout thresholds are applied` | Verify different timeout thresholds for different statuses | Waiting users expired at 1min, active users at 5min |

### 8. Waiting User Queue Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given 10 waiting users with valid heartbeats, when pool processor runs, then all users remain in queue with correct positions` | Verify queue stability with active heartbeats | All users stay WAITING, positions unchanged |
| `Given waiting user at position 3 with heartbeat timeout, when pool processor runs, then user is removed and positions are updated` | Verify queue position recalculation after removal | User 3 removed, users 4-10 move up one position |
| `Given multiple waiting users with heartbeat timeouts, when pool processor runs, then all expired users are removed and positions recalculated` | Verify batch removal and position updates | All expired users removed, remaining users have sequential positions |
| `Given waiting user with heartbeat timeout and active pool has space, when pool processor runs, then expired user is not moved to active pool` | Verify expired users are not promoted | Expired user marked as EXPIRED, not moved to ACTIVE |

### 9. Queue Position and FIFO Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given queue with users joined at different times, when pool processor runs, then users are selected in exact join order` | Verify strict FIFO ordering | Users moved to pool in exact chronological join order |
| `Given user leaves queue and rejoins, when pool processor runs, then user gets new position at end of queue` | Verify queue position reset on rejoin | Rejoined user placed at end of current queue |
| `Given queue positions after user removals, when pool processor runs, then remaining users maintain relative order` | Verify queue integrity after removals | Relative order preserved, positions updated correctly |

### 9. Configuration-Based Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given sale is paused in configuration, when pool processor runs, then no queue advancement occurs` | Verify sale pause functionality | No users moved, queue remains static |
| `Given sale end time is reached, when pool processor runs, then queue processing stops` | Verify sale end time enforcement | Queue processing halts, users notified |
| `Given maximum queue size is reduced, when pool processor runs, then excess users are handled appropriately` | Verify dynamic queue size changes | Queue size adjusted, excess users managed |

## Test Implementation Requirements

### Required Helper Functions
- **Database Operations**: Functions to seed queue users, set heartbeat timestamps for different user statuses, create payment timeouts
- **Heartbeat Management**: Functions to set waiting user heartbeats (1-minute timeout), set active user heartbeats (5-minute timeout)
- **Queue Management**: Functions to verify queue positions, check queue integrity after user removals
- **Pool Management**: Functions to trigger pool processor, get pool statistics, manage pool capacity
- **Assertions**: Functions to verify user status, queue positions, pool capacity, NFT lock status
- **Test Data**: Functions to generate wallet addresses, create JWT tokens, manage session IDs

### Test Data Patterns
- **Standard Test Users**: Consistent structure for wallet address, status, timestamps, session IDs
- **Pool Configuration**: Test-specific settings for pool size, timeouts, sale status
- **Queue Scenarios**: Various queue states with different user counts and statuses
- **Timeout Scenarios**: Users with different heartbeat and payment timeout conditions

## Success Criteria

- All tests pass consistently in SIT environment
- Tests complete within reasonable time limits (< 5 minutes total)
- Database cleanup is thorough and reliable
- Tests provide comprehensive coverage of Pool Processor functionality
- Error scenarios are properly handled and logged
- Performance benchmarks are met consistently
- Test data isolation prevents cross-test contamination
