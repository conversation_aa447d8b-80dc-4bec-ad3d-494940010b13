# Pool Processor Background Job E2E Test Cases

## Overview

This document defines comprehensive End-to-End test cases for the Pool Processor background job flow using Vitest with real MongoDB database connections. The tests focus on queue management, FIFO processing, user selection, payment timeout handling, and pool state transitions.

## Test Framework Configuration

### Vitest Setup
- **Framework**: Vitest E2E testing with real MongoDB connections
- **Environment**: SIT (System Integration Testing) environment
- **Database**: Real MongoDB database with seeding and cleanup utilities
- **Test Timeout**: 30 seconds per test
- **Server**: Real dcasks-backend server running on port 9000

### Test File Structure
```
test/e2e/pool-processor.e2e.spec.ts
```

### Database Setup Requirements
```bash
# Before tests: Seed database with test data
yarn db:seed

# After tests: Clean up database
yarn db:cleanup

# Complete workflow
yarn test:e2e:full
```

## Test Organization

Tests are organized into functional groups following the Given/When/Then pattern:

1. **Queue Management Tests** - Basic queue operations and FIFO processing
2. **Pool State Transition Tests** - User status changes and pool management
3. **Payment Timeout Handling Tests** - Payment expiration and cleanup
4. **Error Scenario Tests** - Edge cases and error conditions
5. **Performance Tests** - Load testing and concurrent operations

## Test Cases

### 1. Queue Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given queue has 5 waiting users, when pool processor runs, then first user is selected for purchase` | Verify FIFO queue processing moves first waiting user to active pool | First user status changes from WAITING to ACTIVE |
| `Given active pool is at capacity (10 users), when pool processor runs, then no new users are moved from queue` | Verify pool capacity limits are respected | Queue users remain in WAITING status |
| `Given queue has 10 users and pool has space for 5, when pool processor runs, then exactly 5 users are moved to active pool` | Verify batch processing respects available pool capacity | Exactly 5 users moved to ACTIVE status |
| `Given empty queue, when pool processor runs, then no errors occur and pool remains unchanged` | Verify graceful handling of empty queue scenarios | No errors, pool statistics unchanged |
| `Given queue with expired sessions, when pool processor runs, then expired sessions are cleaned up` | Verify automatic cleanup of expired queue sessions | Expired sessions removed from queue |

### 2. Pool State Transition Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given user in active pool with heartbeat timeout, when pool processor runs, then user is marked as expired` | Verify heartbeat timeout detection (5-minute threshold) | User status changes from ACTIVE to EXPIRED |
| `Given user completes NFT selection, when pool processor runs, then user is removed from active pool` | Verify completed users are removed from pool tracking | User removed from active pool, pool capacity updated |
| `Given user in SELECTING_NFT status with selection timeout, when pool processor runs, then user is expired and removed` | Verify selection window timeout handling (5 minutes) | User status changes to EXPIRED, removed from pool |
| `Given multiple users with different statuses, when pool processor runs, then each user is processed according to their status` | Verify correct status-based processing logic | Each user processed correctly based on current status |
| `Given user session expires during active pool processing, when pool processor runs, then session is cleaned up and pool rebalanced` | Verify session cleanup and pool rebalancing | Session removed, pool statistics updated |

### 3. Payment Timeout Handling Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given user with payment timeout, when pool processor runs, then user is removed and next user is selected` | Verify payment timeout triggers user removal and queue advancement | Timed-out user removed, next user moved to ACTIVE |
| `Given multiple users with payment timeouts, when pool processor runs, then all timed-out users are processed` | Verify batch processing of multiple payment timeouts | All timed-out users removed, pool refilled from queue |
| `Given user payment expires during pool processing, when pool processor runs, then NFT lock is released` | Verify NFT lock release for payment timeouts | NFT status changes from LOCKED to AVAILABLE |
| `Given payment timeout with related order, when pool processor runs, then order status is updated to expired` | Verify order status updates for payment timeouts | Order status changes to EXPIRED |

### 4. Error Scenario Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given database connection error, when pool processor runs, then error is logged and processing continues` | Verify graceful error handling for database issues | Error logged, no system crash |
| `Given corrupted session data, when pool processor runs, then corrupted sessions are cleaned up` | Verify handling of invalid session data | Corrupted sessions removed, valid sessions processed |
| `Given sale configuration is paused, when pool processor runs, then no queue processing occurs` | Verify respect for sale configuration settings | No users moved, queue remains unchanged |
| `Given invalid pool tracking data, when pool processor runs, then pool tracking is reset and rebuilt` | Verify recovery from corrupted pool tracking | Pool tracking reset, rebuilt from current sessions |
| `Given concurrent pool processor executions, when multiple instances run, then no duplicate processing occurs` | Verify thread safety and concurrent execution handling | No duplicate user processing, consistent state |

### 5. Performance Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given 1000 users in queue, when pool processor runs, then processing completes within 10 seconds` | Verify performance with large queue sizes | Processing completes within time limit |
| `Given 100 active users with mixed statuses, when pool processor runs, then all users are processed efficiently` | Verify efficient processing of full active pool | All users processed within performance threshold |
| `Given rapid heartbeat updates during processing, when pool processor runs, then no race conditions occur` | Verify handling of concurrent heartbeat updates | Consistent state, no data corruption |

## Test Implementation Structure

### Complete Test Implementation Example

```typescript
import { describe, it, expect, beforeAll, beforeEach, afterEach } from 'vitest';
import { MongoClient, Db } from 'mongodb';
import request from 'supertest';
import { JwtService } from '@nestjs/jwt';
import { SYSTEM_ROLES } from 'src/common/enum';

describe('Pool Processor Background Job E2E Tests', () => {
  let db: Db;
  let jwtService: JwtService;
  const baseURL = process.env.TEST_SERVER_URL || 'http://localhost:9000';
  const dbUri = process.env.DATABASE_URI || 'mongodb://localhost:27017/dcasks-sit';

  beforeAll(async () => {
    // Verify test environment
    const nodeEnv = process.env.NODE_ENV;
    if (nodeEnv !== 'sit' && nodeEnv !== 'test') {
      throw new Error(`Unsafe environment: ${nodeEnv}. Use 'sit' or 'test' only.`);
    }

    // Setup database connection
    const client = new MongoClient(dbUri);
    await client.connect();
    db = client.db();

    // Setup JWT service for token generation
    jwtService = new JwtService({
      secret: process.env.JWT_SECRET || 'djW1mJFQ2B',
      signOptions: { expiresIn: '1h' },
    });

    console.log(`🎯 Running Pool Processor tests against: ${baseURL}`);
  });

  beforeEach(async () => {
    // Clean up any existing test data
    await cleanupTestData();

    // Seed fresh test data for each test
    await seedBasicTestData();
  });

  afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData();
  });

  // Helper Functions
  function generateWalletAddress(): string {
    return `0x${Math.random().toString(16).substring(2, 42)}`;
  }

  function createJwtToken(walletAddress: string): string {
    return jwtService.sign({
      publicAddress: walletAddress,
      version: '1.0.0',
      role: SYSTEM_ROLES.USER_ROLE,
      email: `${walletAddress}@test.com`,
    });
  }

  async function seedQueueUsers(count: number, status: string = 'WAITING'): Promise<string[]> {
    const users = [];
    const baseTime = new Date();

    for (let i = 0; i < count; i++) {
      const walletAddress = generateWalletAddress();
      const sessionId = `session_${Date.now()}_${i}`;
      const joinTime = new Date(baseTime.getTime() + (i * 1000)); // Stagger join times

      await db.collection('queue_sessions').insertOne({
        sessionId,
        walletAddress,
        status,
        joinedAt: joinTime,
        lastHeartbeat: new Date(),
        expiresAt: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes
        position: i + 1,
      });

      users.push(sessionId);
    }

    return users;
  }

  async function setUserHeartbeat(sessionId: string, minutesAgo: number): Promise<void> {
    const heartbeatTime = new Date(Date.now() - (minutesAgo * 60 * 1000));
    await db.collection('queue_sessions').updateOne(
      { sessionId },
      { $set: { lastHeartbeat: heartbeatTime } }
    );
  }

  async function triggerPoolProcessor(): Promise<void> {
    // Trigger the pool processor by calling the internal endpoint or waiting for scheduled execution
    // This might require a special test endpoint or direct service call
    await request(baseURL)
      .post('/api/v1/internal/pool-processor/trigger')
      .set('Authorization', 'Bearer test-admin-token')
      .expect(200);
  }

  async function expectUserStatus(sessionId: string, expectedStatus: string): Promise<void> {
    const session = await db.collection('queue_sessions').findOne({ sessionId });
    expect(session?.status).toBe(expectedStatus);
  }

  async function getPoolStatistics(): Promise<any> {
    const activeCount = await db.collection('queue_sessions').countDocuments({ status: 'ACTIVE' });
    const waitingCount = await db.collection('queue_sessions').countDocuments({ status: 'WAITING' });
    const poolTracking = await db.collection('active_pool_tracking').findOne({});

    return {
      activeCount,
      waitingCount,
      poolCapacity: poolTracking?.currentSize || 0,
      maxPoolSize: poolTracking?.maxSize || 100,
    };
  }

  async function seedBasicTestData(): Promise<void> {
    // Seed sale configuration
    await db.collection('sale_config').insertOne({
      saleActive: true,
      salePaused: false,
      maxQueueSize: 1000,
      maxActivePoolSize: 100,
      heartbeatTimeoutMinutes: 5,
      selectionTimeoutMinutes: 5,
    });

    // Seed active pool tracking
    await db.collection('active_pool_tracking').insertOne({
      currentSize: 0,
      maxSize: 100,
      lastUpdated: new Date(),
    });
  }

  async function cleanupTestData(): Promise<void> {
    await db.collection('queue_sessions').deleteMany({ sessionId: /^session_/ });
    await db.collection('active_pool_tracking').deleteMany({});
    await db.collection('sale_config').deleteMany({});
    await db.collection('orders').deleteMany({ walletAddress: /^0x/ });
    await db.collection('payment_records').deleteMany({ orderId: /^test_/ });
  }

  describe('Queue Management Tests', () => {
    it('Given queue has 5 waiting users, when pool processor runs, then first user is selected for purchase', async () => {
      // Arrange
      const userSessions = await seedQueueUsers(5, 'WAITING');
      const initialStats = await getPoolStatistics();

      console.log('📊 Initial state:', {
        waitingUsers: initialStats.waitingCount,
        activeUsers: initialStats.activeCount
      });

      // Act
      await triggerPoolProcessor();

      // Wait for processing to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Assert
      const finalStats = await getPoolStatistics();
      expect(finalStats.activeCount).toBeGreaterThan(initialStats.activeCount);

      // Verify first user was moved to active
      await expectUserStatus(userSessions[0], 'ACTIVE');

      console.log('✅ Pool processor result:', {
        usersMovedToActive: finalStats.activeCount - initialStats.activeCount,
        remainingInQueue: finalStats.waitingCount
      });
    });

    it('Given active pool is at capacity (100 users), when pool processor runs, then no new users are moved from queue', async () => {
      // Arrange - Create 100 active users and 10 waiting users
      await seedQueueUsers(100, 'ACTIVE');
      const waitingUsers = await seedQueueUsers(10, 'WAITING');

      // Update pool tracking to reflect full capacity
      await db.collection('active_pool_tracking').updateOne(
        {},
        { $set: { currentSize: 100 } }
      );

      const initialStats = await getPoolStatistics();

      // Act
      await triggerPoolProcessor();
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Assert
      const finalStats = await getPoolStatistics();
      expect(finalStats.activeCount).toBe(100); // Should remain at capacity
      expect(finalStats.waitingCount).toBe(10); // No users should be moved

      // Verify waiting users are still waiting
      for (const sessionId of waitingUsers) {
        await expectUserStatus(sessionId, 'WAITING');
      }

      console.log('✅ Capacity limit respected:', finalStats);
    });
  });

  describe('Pool State Transition Tests', () => {
    it('Given user in active pool with heartbeat timeout, when pool processor runs, then user is marked as expired', async () => {
      // Arrange
      const userSessions = await seedQueueUsers(1, 'ACTIVE');
      const sessionId = userSessions[0];

      // Set heartbeat to 6 minutes ago (past 5-minute threshold)
      await setUserHeartbeat(sessionId, 6);

      console.log('📊 User heartbeat set to 6 minutes ago');

      // Act
      await triggerPoolProcessor();
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Assert
      await expectUserStatus(sessionId, 'EXPIRED');

      const finalStats = await getPoolStatistics();
      expect(finalStats.activeCount).toBe(0); // User should be removed from active pool

      console.log('✅ Heartbeat timeout handled correctly');
    });
  });
});
```

### Database Seeding Requirements

For each test, the following data should be seeded:

1. **Sale Configuration**: Active sale with proper queue and pool limits
2. **Queue Sessions**: Test users in various states (WAITING, ACTIVE, SELECTING_NFT, EXPIRED)
3. **Active Pool Tracking**: Current pool statistics and capacity tracking
4. **NFT Inventory**: Available NFTs for selection and locking
5. **Orders**: Test orders in various payment states
6. **Payment Records**: Test payments with different timeout scenarios

### Test Data Cleanup

After each test:
1. Remove all test queue sessions
2. Reset active pool tracking
3. Unlock any test NFT locks
4. Remove test orders and payment records
5. Reset sale configuration to default test state

## Integration with Existing E2E Framework

These tests will integrate with the existing E2E testing framework:

- **Database Scripts**: Use existing `yarn db:seed` and `yarn db:cleanup` commands
- **Test Environment**: Run in SIT environment with real MongoDB connections
- **Test Execution**: Use `yarn test:e2e:full` workflow for complete testing
- **Error Handling**: Follow existing error logging and reporting patterns

## Advanced Test Scenarios

### 6. Pool Capacity Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given pool at 9/10 capacity with 1 user completing, when pool processor runs, then 1 new user is added from queue` | Verify precise capacity management | Exactly 1 user moved from queue to maintain 10 capacity |
| `Given pool capacity changes during processing, when pool processor runs, then new capacity limits are respected` | Verify dynamic capacity adjustment | Pool size adjusts to new configuration limits |
| `Given pool with users in different selection stages, when pool processor runs, then capacity calculation accounts for all active users` | Verify accurate capacity calculation | Pool capacity correctly calculated including all active states |

### 7. Heartbeat and Session Management Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given user with recent heartbeat (< 5 minutes), when pool processor runs, then user remains in active pool` | Verify heartbeat validation logic | User stays ACTIVE with valid heartbeat |
| `Given user with heartbeat exactly at 5-minute threshold, when pool processor runs, then user is marked as expired` | Verify precise timeout boundary handling | User marked as EXPIRED at exact threshold |
| `Given user sends heartbeat during pool processing, when pool processor runs, then updated heartbeat is respected` | Verify real-time heartbeat updates | User remains active with updated heartbeat |
| `Given multiple users with staggered heartbeat timeouts, when pool processor runs, then users are expired in correct order` | Verify batch timeout processing | Users expired based on individual timeout times |

### 8. Queue Position and FIFO Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given queue with users joined at different times, when pool processor runs, then users are selected in exact join order` | Verify strict FIFO ordering | Users moved to pool in exact chronological join order |
| `Given user leaves queue and rejoins, when pool processor runs, then user gets new position at end of queue` | Verify queue position reset on rejoin | Rejoined user placed at end of current queue |
| `Given queue positions after user removals, when pool processor runs, then remaining users maintain relative order` | Verify queue integrity after removals | Relative order preserved, positions updated correctly |

### 9. Configuration-Based Tests

| Test Name | Description | Expected Outcome |
|-----------|-------------|------------------|
| `Given sale is paused in configuration, when pool processor runs, then no queue advancement occurs` | Verify sale pause functionality | No users moved, queue remains static |
| `Given sale end time is reached, when pool processor runs, then queue processing stops` | Verify sale end time enforcement | Queue processing halts, users notified |
| `Given maximum queue size is reduced, when pool processor runs, then excess users are handled appropriately` | Verify dynamic queue size changes | Queue size adjusted, excess users managed |

## Test Implementation Details

### Helper Functions

```typescript
// Database helper functions
async function seedQueueUsers(count: number, status: string = 'WAITING'): Promise<string[]>
async function createActivePoolUser(walletAddress: string): Promise<string>
async function setUserHeartbeat(sessionId: string, minutesAgo: number): Promise<void>
async function createPaymentTimeout(sessionId: string, minutesAgo: number): Promise<void>
async function getPoolStatistics(): Promise<PoolStats>
async function triggerPoolProcessor(): Promise<void>

// Assertion helper functions
async function expectUserStatus(sessionId: string, expectedStatus: string): Promise<void>
async function expectQueuePosition(sessionId: string, expectedPosition: number): Promise<void>
async function expectPoolCapacity(expectedCount: number): Promise<void>
async function expectNFTLockStatus(nftId: string, expectedStatus: string): Promise<void>
```

### Test Data Patterns

```typescript
// Standard test user pattern
const testUser = {
  walletAddress: generateWalletAddress(),
  status: 'WAITING',
  joinedAt: new Date(),
  lastHeartbeat: new Date(),
  sessionId: generateSessionId()
};

// Pool processor test configuration
const testConfig = {
  maxActivePoolSize: 100,
  heartbeatTimeoutMinutes: 5,
  selectionTimeoutMinutes: 5,
  saleActive: true,
  salePaused: false
};
```

### Performance Benchmarks

- **Queue Processing**: < 2 seconds for 100 users
- **Pool Management**: < 1 second for full pool (100 users)
- **Heartbeat Validation**: < 500ms for 100 active users
- **Database Operations**: < 100ms per user operation
- **Memory Usage**: < 50MB during peak processing

## Monitoring and Observability

### Test Metrics to Track

1. **Processing Time**: Time taken for each pool processor cycle
2. **Queue Advancement Rate**: Users moved from queue to pool per cycle
3. **Timeout Detection Rate**: Percentage of timeouts correctly identified
4. **Error Rate**: Percentage of failed operations during processing
5. **Database Performance**: Query execution times and connection usage

### Logging Requirements

```typescript
// Test execution logging
console.log('🎯 Starting Pool Processor Test:', testName);
console.log('📊 Initial State:', { queueSize, poolSize, activeUsers });
console.log('⚡ Processing Result:', { usersAdvanced, usersExpired, errors });
console.log('✅ Final State:', { newQueueSize, newPoolSize, newActiveUsers });
```

## Success Criteria

- All tests pass consistently in SIT environment
- Tests complete within reasonable time limits (< 5 minutes total)
- Database cleanup is thorough and reliable
- Tests provide comprehensive coverage of Pool Processor functionality
- Error scenarios are properly handled and logged
- Performance benchmarks are met consistently
- Test data isolation prevents cross-test contamination
