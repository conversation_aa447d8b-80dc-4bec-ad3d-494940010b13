# Phase 2.5 Implementation Plan: API Documentation & Testing
## Sailing Whisky NFT Sale System

**Phase**: 2.5 (Bridge Phase)  
**Duration**: 2-3 hours  
**Prerequisites**: Phase 2 (Core Backend Services) - ✅ COMPLETED  
**Next Phase**: Phase 3 (Payment Integration Layer)

---

## 🎯 **PHASE OBJECTIVES**

Bridge Phase 2 and Phase 3 by adding essential documentation and testing infrastructure to ensure:
1. **API Documentation**: Complete Swagger documentation for all 13 REST endpoints
2. **Testing Infrastructure**: Vitest-based E2E testing with real database integration
3. **Developer Experience**: Clear setup instructions and testing workflows
4. **Quality Assurance**: One complete E2E test case to validate core user flow

---

## 📋 **IMPLEMENTATION TASKS**

### **Task 2.5.1: Swagger API Documentation Setup**

#### **2.5.1.1: Install and Configure @nestjs/swagger**
- **Action**: Install required packages for Swagger documentation
- **Commands**:
  ```bash
  cd apps/dcasks-backend
  npm install @nestjs/swagger swagger-ui-express
  npm install -D @types/swagger-ui-express
  ```
- **Files to Modify**:
  - `apps/dcasks-backend/package.json` (dependencies)
  - `apps/dcasks-backend/src/main.ts` (Swagger setup)

#### *********: Configure Swagger in Main Application**
- **Action**: Set up Swagger documentation at `/api/v1/docs` endpoint
- **Implementation**:
  ```typescript
  // In main.ts
  import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
  
  const config = new DocumentBuilder()
    .setTitle('DCasks NFT Sale API')
    .setDescription('Sailing Whisky NFT Sale System API Documentation')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, document);
  ```

#### *********: Document Queue Management Endpoints (4 endpoints)**
- **Files to Update**:
  - `apps/dcasks-backend/src/nft-sale/controllers/queue.controller.ts`
  - `apps/dcasks-backend/src/nft-sale/dto/queue.dto.ts`
- **Decorators to Add**:
  - `@ApiTags('Queue Management')`
  - `@ApiOperation()` for each endpoint
  - `@ApiResponse()` for success/error responses
  - `@ApiBody()` for request bodies
  - `@ApiParam()` for path parameters

#### *********: Document Order Management Endpoints (4 endpoints)**
- **Files to Update**:
  - `apps/dcasks-backend/src/nft-sale/controllers/order.controller.ts`
  - `apps/dcasks-backend/src/nft-sale/dto/order.dto.ts`
- **Documentation Focus**:
  - Order creation flow
  - Order status tracking
  - Wallet-based order history
  - Error handling scenarios

#### *********: Document NFT Management Endpoints (5 endpoints)**
- **Files to Update**:
  - `apps/dcasks-backend/src/nft-sale/controllers/nft.controller.ts`
  - `apps/dcasks-backend/src/nft-sale/dto/nft.dto.ts`
- **Documentation Focus**:
  - NFT availability checking
  - NFT locking mechanism
  - Filtering and pagination
  - Admin unlock functionality

#### *********: Enhance DTOs with Swagger Decorators**
- **Action**: Add comprehensive API property decorators to all DTOs
- **Decorators to Add**:
  - `@ApiProperty()` with examples and descriptions
  - `@ApiPropertyOptional()` for optional fields
  - Validation decorators integration
  - Enum documentation

---

### **Task 2.5.2: Vitest Testing Infrastructure Setup**

#### *********: Install and Configure Vitest**
- **Action**: Install Vitest and related testing packages
- **Commands**:
  ```bash
  cd apps/dcasks-backend
  npm install -D vitest @vitest/ui @nestjs/testing
  npm install -D mongodb-memory-server
  ```
- **Files to Create**:
  - `apps/dcasks-backend/vitest.config.ts`
  - `apps/dcasks-backend/test/vitest-setup.ts`

#### *********: Configure Vitest for NestJS**
- **Action**: Set up Vitest configuration for NestJS testing
- **Configuration**:
  ```typescript
  // vitest.config.ts
  import { defineConfig } from 'vitest/config';
  
  export default defineConfig({
    test: {
      globals: true,
      environment: 'node',
      setupFiles: ['./test/vitest-setup.ts'],
      testTimeout: 30000,
    },
  });
  ```

#### *********: Database Testing Setup**
- **Action**: Configure MongoDB Memory Server for isolated testing
- **Files to Create**:
  - `apps/dcasks-backend/test/database-test-utils.ts`
  - `apps/dcasks-backend/test/test-data-factory.ts`
- **Features**:
  - In-memory MongoDB instance
  - Database seeding utilities
  - Cleanup mechanisms
  - Transaction support

#### *********: Test Data Factories**
- **Action**: Create data factories for all 7 MongoDB collections
- **Collections to Cover**:
  - `orders` - Order test data with various statuses
  - `queue_sessions` - Queue session test scenarios
  - `nft_inventory` - NFT test data with different availability states
  - `payment_records` - Payment test records
  - `sale_config` - Sale configuration test data
  - `active_pool_tracking` - Pool tracking test data
  - `admin_actions` - Admin action test records

---

### **Task 2.5.3: E2E Test Implementation**

#### **2.5.3.1: Test Environment Setup**
- **Action**: Create isolated test environment for E2E testing
- **Files to Create**:
  - `apps/dcasks-backend/test/e2e/nft-sale-flow.e2e.spec.ts`
  - `apps/dcasks-backend/test/e2e/test-app.ts`
- **Setup Requirements**:
  - Isolated NestJS application instance
  - Separate test database
  - Mock external services
  - Authentication bypass for testing

#### **2.5.3.2: Core User Flow E2E Test**
- **Test Scenario**: "User joins queue and creates order flow"
- **Test Steps**:
  1. **Setup**: Seed test data (NFTs, sale config)
  2. **Step 1**: `POST /api/v1/collaborations/queue` - User joins queue
  3. **Step 2**: `GET /api/v1/collaborations/queue/status` - Check queue position
  4. **Step 3**: `POST /api/v1/collaborations/nfts/lock` - Lock selected NFT
  5. **Step 4**: `POST /api/v1/collaborations/orders` - Create order
  6. **Verification**: Database state validation at each step
  7. **Cleanup**: Remove test data

#### *********: Database State Verification**
- **Action**: Implement comprehensive database state checking
- **Verification Points**:
  - Queue session creation and status updates
  - NFT lock status changes
  - Order creation with correct relationships
  - Pool tracking updates
  - Admin action logging

#### *********: Test Cleanup and Isolation**
- **Action**: Ensure tests run independently and clean up properly
- **Implementation**:
  - Database reset between tests
  - Transaction rollback mechanisms
  - Memory cleanup
  - Service state reset

---

## 🛠 **TECHNICAL SPECIFICATIONS**

### **Swagger Configuration**
```typescript
// Swagger setup in main.ts
const config = new DocumentBuilder()
  .setTitle('DCasks NFT Sale API')
  .setDescription('Sailing Whisky NFT Sale System API Documentation')
  .setVersion('1.0')
  .addBearerAuth({
    type: 'http',
    scheme: 'bearer',
    bearerFormat: 'JWT',
  })
  .addTag('Queue Management', 'Queue and session management endpoints')
  .addTag('Order Management', 'Order creation and tracking endpoints')
  .addTag('NFT Management', 'NFT availability and locking endpoints')
  .build();
```

### **Vitest Configuration**
```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    setupFiles: ['./test/vitest-setup.ts'],
    testTimeout: 30000,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
    },
  },
});
```

### **Test Database Setup**
```typescript
// database-test-utils.ts
export class TestDatabaseManager {
  private mongoServer: MongoMemoryServer;
  
  async setupDatabase(): Promise<string> {
    this.mongoServer = await MongoMemoryServer.create();
    return this.mongoServer.getUri();
  }
  
  async cleanupDatabase(): Promise<void> {
    await this.mongoServer.stop();
  }
}
```

---

## 📁 **FILE STRUCTURE**

```
apps/dcasks-backend/
├── src/
│   ├── main.ts                     # ← Swagger setup
│   └── nft-sale/
│       ├── controllers/            # ← Add Swagger decorators
│       │   ├── queue.controller.ts
│       │   ├── order.controller.ts
│       │   └── nft.controller.ts
│       └── dto/                    # ← Enhance with API properties
│           ├── queue.dto.ts
│           ├── order.dto.ts
│           └── nft.dto.ts
├── test/
│   ├── vitest-setup.ts            # ← New: Vitest configuration
│   ├── database-test-utils.ts     # ← New: Database utilities
│   ├── test-data-factory.ts       # ← New: Test data factories
│   └── e2e/
│       ├── test-app.ts            # ← New: Test app setup
│       └── nft-sale-flow.e2e.spec.ts # ← New: E2E test
├── vitest.config.ts               # ← New: Vitest configuration
└── package.json                   # ← Updated dependencies
```

---

## 🎯 **DELIVERABLES**

### **1. Working Swagger Documentation**
- **URL**: `http://localhost:3000/api/v1/docs`
- **Features**:
  - Interactive API explorer
  - Complete endpoint documentation
  - Request/response schemas
  - Authentication examples
  - Error code documentation

### **2. Vitest Testing Infrastructure**
- **Configuration**: Complete Vitest setup for NestJS
- **Database**: MongoDB Memory Server integration
- **Utilities**: Test data factories and cleanup utilities
- **Scripts**: npm scripts for running tests

### **3. Complete E2E Test Case**
- **Test**: "User joins queue and creates order flow"
- **Coverage**: 4-step user journey with database verification
- **Isolation**: Independent test execution with cleanup
- **Documentation**: Clear test scenario documentation

### **4. Developer Documentation**
- **Setup Instructions**: How to run tests and view API docs
- **Testing Guide**: How to write additional E2E tests
- **API Guide**: How to use the Swagger documentation
- **Troubleshooting**: Common issues and solutions

---

## ⚡ **EXECUTION PLAN**

### **Phase 2.5.1: Swagger Setup (45 minutes)**
1. Install @nestjs/swagger packages
2. Configure Swagger in main.ts
3. Add basic API tags and setup

### **Phase 2.5.2: API Documentation (60 minutes)**
1. Document Queue Management endpoints (15 min)
2. Document Order Management endpoints (15 min)
3. Document NFT Management endpoints (20 min)
4. Enhance DTOs with API properties (10 min)

### **Phase 2.5.3: Vitest Infrastructure (45 minutes)**
1. Install and configure Vitest (15 min)
2. Set up database testing utilities (15 min)
3. Create test data factories (15 min)

### **Phase 2.5.4: E2E Test Implementation (60 minutes)**
1. Set up test environment (20 min)
2. Implement core user flow test (30 min)
3. Add database verification and cleanup (10 min)

### **Phase 2.5.5: Documentation & Verification (30 minutes)**
1. Create developer setup instructions (15 min)
2. Test complete workflow (10 min)
3. Update implementation logs (5 min)

---

## ✅ **SUCCESS CRITERIA**

1. **Swagger Documentation**: All 13 endpoints documented and accessible at `/api/v1/docs`
2. **Vitest Setup**: Tests run successfully with `npm run test:e2e:vitest`
3. **E2E Test**: Complete user flow test passes with database verification
4. **Documentation**: Clear setup instructions for other developers
5. **Integration**: No breaking changes to existing Phase 2 implementation

---

## 🔄 **NEXT PHASE PREPARATION**

This phase prepares for **Phase 3: Payment Integration Layer** by:
- **API Documentation**: Clear interface contracts for payment integration
- **Testing Infrastructure**: Foundation for testing payment flows
- **Quality Assurance**: Validated core functionality before adding payments
- **Developer Experience**: Improved debugging and development workflow

**Phase 3 Readiness**: 100% - All prerequisites met for payment integration implementation.
