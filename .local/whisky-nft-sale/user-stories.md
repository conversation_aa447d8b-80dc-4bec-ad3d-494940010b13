# User Stories: Sailing Whisky NFT Sale System
## Based on System Architecture v2 & Implementation Plan v2

This document contains comprehensive user stories covering the complete NFT sale workflow from queue entry through NFT transfer completion.

## 1. Queue Management Stories

### US-Q001: Join Sale Queue
**As a** user with a crypto wallet  
**I want to** join the NFT sale queue by connecting my wallet  
**So that** I can participate in the limited-time NFT sale with fair FIFO ordering

**Acceptance Criteria:**
- ✅ User can connect their crypto wallet to the landing page
- ✅ System validates wallet connection and signature
- ✅ User is added to queue with assigned position (max 100 users)
- ✅ User receives queue position and estimated wait time
- ✅ Queue session is created with expiration timeout
- ✅ User cannot join queue twice with same wallet address

**Technical Notes:**
- API: `POST /api/v1/collaborations/queue`
- Creates record in `queue_sessions` collection
- Validates against `sale_config.maxQueueSize`

### US-Q002: Monitor Queue Position
**As a** queued user
**I want to** see my current position and estimated wait time in real-time
**So that** I know when I'll be able to select an NFT

**Acceptance Criteria:**
- ✅ User sees current queue position updated every 2 seconds
- ✅ User sees estimated wait time based on pool processing
- ✅ User sees pool status (waiting, active, processing)
- ✅ Position updates automatically as other users complete or timeout
- ✅ User receives notification when entering active pool
- ✅ Session remains active with heartbeat mechanism

**Technical Notes:**
- API: `GET /api/v1/collaborations/queue/status`
- Polling mechanism with exponential backoff
- Updates `queue_sessions.lastHeartbeat`

### US-Q003: Enter Active Pool
**As a** queued user with valid heartbeat
**I want to** be automatically moved to the active pool when it's my turn
**So that** I can begin selecting an NFT within the time window

**Acceptance Criteria:**
- ✅ System continuously manages active pool up to 100 concurrent users
- ✅ Only users with valid heartbeats (within 60 seconds) are eligible for promotion
- ✅ User is notified when entering active pool
- ✅ User's status changes from 'waiting' to 'active'
- ✅ User receives 5-minute selection window
- ✅ Selection timer is displayed and counts down
- ✅ User can see available NFTs for selection
- ✅ User must maintain heartbeat every 5 minutes while in active pool

**Technical Notes:**
- Managed by DCasks Job Service pool processor
- Updates `queue_sessions.status` to 'active'
- Tracks active pool size dynamically
- Promotion occurs after waiting user heartbeat monitoring


### US-Q004: Handle Waiting User Heartbeat Timeout
**As a** user waiting in queue who stops sending heartbeats
**I want to** be automatically removed from the queue after 1 minute of inactivity
**So that** inactive users don't block queue progression for active participants

**Acceptance Criteria:**
- ✅ User is automatically removed if no heartbeat received for 60 seconds
- ✅ User receives notification about heartbeat timeout removal
- ✅ Remaining users' queue positions are automatically recalculated
- ✅ User can rejoin queue (goes to end of line) after timeout
- ✅ System maintains queue integrity with sequential positions
- ✅ Timeout monitoring occurs before any queue advancement processing

**Technical Notes:**
- Pool Processor monitors `WAITING_USER_HEARTBEAT_TIMEOUT_MS` (60,000ms)
- Updates `queue_sessions.status` to 'expired'
- Triggers `updateQueuePositions()` for remaining users
- Heartbeat monitoring is first step in processing flow

### US-Q005: Experience Queue Position Recalculation
**As a** user waiting in queue when other users are removed due to timeouts
**I want to** see my position automatically update to reflect the new queue order
**So that** I have accurate wait time estimates and understand my progress

**Acceptance Criteria:**
- ✅ User's position decreases when users ahead are removed for heartbeat timeout
- ✅ Position updates are reflected in real-time on the interface
- ✅ Wait time estimates are recalculated based on new position
- ✅ User receives notification when position changes significantly (>5 positions)
- ✅ Queue maintains sequential numbering (1, 2, 3...) with no gaps
- ✅ Position changes are processed before any new users enter active pool

**Technical Notes:**
- `updateQueuePositions()` assigns sequential positions after removals
- API: `GET /api/v1/collaborations/queue/status` returns updated position
- Position changes trigger frontend notifications
- Processing order: heartbeat monitoring → position updates → pool advancement

### US-Q006: Handle Active Pool Heartbeat Timeout
**As a** user in the active pool who stops sending heartbeats
**I want to** be automatically removed from the active pool after 5 minutes of inactivity
**So that** my inactive session doesn't prevent other users from entering the active pool

**Acceptance Criteria:**
- ✅ User is automatically removed if no heartbeat received for 300 seconds (5 minutes)
- ✅ User receives notification about heartbeat timeout removal from active pool
- ✅ User's session status changes from 'active' to 'expired'
- ✅ Active pool capacity is updated to allow new users from queue
- ✅ Any locked NFTs are automatically released
- ✅ User can rejoin queue (goes to end of line) after timeout
- ✅ Next eligible user from queue is automatically promoted to active pool

**Technical Notes:**
- Pool Processor monitors `ACTIVE_USER_HEARTBEAT_TIMEOUT_MS` (300,000ms)
- Updates `queue_sessions.status` to 'expired'
- Releases any NFT locks in `nft_inventory`
- Triggers pool rebalancing and queue advancement

### US-Q007: Handle Payment Status Heartbeat Timeout
**As a** user in payment status who stops sending heartbeats
**I want to** be automatically removed from the active pool after 5 minutes of inactivity
**So that** my inactive session doesn't prevent other users from accessing the system

**Acceptance Criteria:**
- ✅ User is automatically removed if no heartbeat received for 300 seconds (5 minutes)
- ✅ User receives notification about heartbeat timeout during payment
- ✅ User's session status changes from 'payment' to 'expired'
- ✅ Any pending payment sessions are cancelled
- ✅ Any locked NFTs are automatically released
- ✅ Active pool capacity is updated to allow new users from queue
- ✅ User can rejoin queue (goes to end of line) after timeout

**Technical Notes:**
- Pool Processor monitors `ACTIVE_USER_HEARTBEAT_TIMEOUT_MS` (300,000ms) for payment users
- Updates `queue_sessions.status` to 'expired'
- Cancels payment sessions and releases NFT locks
- Triggers pool rebalancing and queue advancement

### US-Q008: Experience Enhanced Processing Flow
**As a** user participating in the NFT sale queue
**I want to** benefit from optimized queue processing that prioritizes active users
**So that** I experience faster queue advancement and more reliable system performance

**Acceptance Criteria:**
- ✅ Inactive waiting users are removed before any queue advancement occurs
- ✅ Queue positions are recalculated before new users enter active pool
- ✅ Only users with valid heartbeats are eligible for active pool promotion
- ✅ Active pool processing occurs after waiting user cleanup
- ✅ System maintains consistent processing order across all operations
- ✅ User experiences improved queue advancement timing
- ✅ System performance is optimized by removing inactive users first

**Technical Notes:**
- Processing order: waiting user monitoring → active pool processing → queue filling → cleanup
- Ensures clean queue state before any advancement operations
- Improves overall system efficiency and user experience

### US-Q009: Understand Heartbeat Requirements
**As a** user participating in the NFT sale
**I want to** understand the heartbeat requirements for different statuses
**So that** I can maintain my session and avoid unexpected timeouts

**Acceptance Criteria:**
- ✅ User sees clear explanation of heartbeat requirements during onboarding
- ✅ User understands 1-minute timeout while waiting in queue
- ✅ User understands 5-minute timeout while in active pool or payment
- ✅ User receives warnings before heartbeat timeout (30 seconds, 10 seconds)
- ✅ User can see their last heartbeat timestamp and next required heartbeat
- ✅ User interface automatically maintains heartbeats when page is active
- ✅ User is notified if heartbeat fails due to network issues

**Technical Notes:**
- Frontend automatically sends heartbeats via polling mechanism
- Warning system based on timeout constants
- Clear documentation of timeout thresholds for each status

### US-Q010: Handle Concurrent Heartbeat Scenarios
**As a** user sending heartbeats while the system processes timeouts
**I want to** have my heartbeat updates respected in real-time
**So that** I'm not incorrectly removed due to timing conflicts

**Acceptance Criteria:**
- ✅ User's heartbeat updates are processed immediately when received
- ✅ User is not removed if heartbeat is sent during timeout processing
- ✅ System maintains data consistency during concurrent operations
- ✅ User receives confirmation that heartbeat was successfully processed
- ✅ Race conditions between heartbeat updates and timeout checks are handled gracefully
- ✅ User's session remains active if heartbeat arrives within timeout window
- ✅ System logs provide clear audit trail of heartbeat timing

**Technical Notes:**
- Database operations use proper locking to prevent race conditions
- Heartbeat timestamps are checked against current time during processing
- Concurrent operation handling ensures data consistency

### US-Q011: Experience Queue Advancement After Timeout Cleanup
**As a** user waiting in queue when timeout cleanup occurs
**I want to** be automatically advanced when space becomes available in the active pool
**So that** I benefit from the removal of inactive users ahead of me

**Acceptance Criteria:**
- ✅ User is automatically promoted when active pool space becomes available
- ✅ Promotion occurs after timeout cleanup and position recalculation
- ✅ User receives notification about advancement due to timeout cleanup
- ✅ User's wait time is reduced when inactive users are removed
- ✅ System maintains fair FIFO order among remaining active users
- ✅ User sees updated queue statistics reflecting cleanup results
- ✅ Advancement happens within the same processing cycle as cleanup

**Technical Notes:**
- Queue advancement occurs after waiting user monitoring and position updates
- Pool filling respects updated queue positions and available capacity
- Processing flow ensures cleanup benefits are immediately applied

## 2. NFT Selection Stories

### US-N001: Browse Available NFTs
**As an** active pool user
**I want to** see all available NFTs with details and pricing
**So that** I can make an informed selection within my time window

**Acceptance Criteria:**
- ✅ User sees gallery of available NFTs with images
- ✅ Each NFT shows name, description, attributes, and price
- ✅ User can view high-resolution images and metadata
- ✅ Only truly available NFTs are shown (not locked by others)
- ✅ NFT availability updates in real-time
- ✅ User sees selection timer counting down

**Technical Notes:**
- API: `GET /api/v1/collaborations/nfts/available`
- Filters `nft_inventory` by status 'available'
- Real-time updates via polling

### US-N002: Select and Lock NFT
**As an** active pool user
**I want to** select an NFT and have it locked for my purchase
**So that** no one else can buy it while I complete payment

**Acceptance Criteria:**
- ✅ User can click to select an available NFT
- ✅ NFT is immediately locked to user's wallet address
- ✅ Lock expires in 5 minutes if payment not completed
- ✅ Other users cannot see or select locked NFT
- ✅ User proceeds to payment method selection
- ✅ Order is created linking user, NFT, and queue session

**Technical Notes:**
- API: `POST /api/v1/collaborations/nfts/lock`
- Creates record in `orders` collection
- Updates `nft_inventory.status` to 'locked'
- Sets `nft_inventory.lockExpiresAt`

### US-N003: Handle NFT Selection Timeout
**As an** active pool user who doesn't select an NFT in time
**I want to** be notified of the timeout and removed from the active pool
**So that** I understand why my session ended and others can proceed

**Acceptance Criteria:**
- ✅ User is notified when 5-minute selection window expires
- ✅ User is removed from active pool automatically
- ✅ User's queue session is marked as expired
- ✅ Pool continues with remaining active users
- ✅ User sees clear explanation and option to rejoin queue
- ✅ Timeout is separate from heartbeat timeout (both must be maintained)
- ✅ User can still timeout due to selection window even with valid heartbeats

**Technical Notes:**
- Job Service monitors `queue_sessions.selectionExpiresAt`
- Updates `queue_sessions.status` to 'expired'
- Triggers pool rebalancing
- Selection timeout is independent of `ACTIVE_USER_HEARTBEAT_TIMEOUT_MS`

### US-N004: Handle NFT Lock Expiration
**As a** user whose NFT lock expires during payment  
**I want to** be notified that my NFT is no longer reserved  
**So that** I understand why my purchase failed and can try again

**Acceptance Criteria:**
- ✅ System automatically unlocks NFT after 5 minutes
- ✅ User is notified of lock expiration
- ✅ NFT becomes available for other users
- ✅ User's order is marked as expired
- ✅ User can rejoin queue to try again

**Technical Notes:**
- Job Service monitors `nft_inventory.lockExpiresAt`
- Updates `nft_inventory.status` to 'available'
- Updates `orders.status` to 'expired'

## 3. Payment Processing Stories

### US-P001: Choose Payment Method
**As a** user with a locked NFT  
**I want to** choose between card payment (Stripe) and crypto payment (NOWPayments)  
**So that** I can pay using my preferred method

**Acceptance Criteria:**
- ✅ User sees two payment options: "Pay with Card" and "Pay with Crypto"
- ✅ Each option shows supported methods (cards vs cryptocurrencies)
- ✅ User sees pricing in USD for both options
- ✅ User sees timeout information (configurable for Stripe, 20min for crypto)
- ✅ Selection leads to appropriate payment flow
- ✅ Payment timer starts counting down

**Technical Notes:**
- Payment method stored in `orders` collection
- Timeout values from `sale_config`
- Leads to payment session creation

### US-P002: Complete Stripe Card Payment
**As a** user who chose card payment  
**I want to** complete payment through Stripe Checkout  
**So that** I can purchase my selected NFT with a credit/debit card

**Acceptance Criteria:**
- ✅ User is redirected to Stripe Checkout session
- ✅ User can enter card details securely
- ✅ Payment is processed within configurable timeout
- ✅ User receives payment confirmation
- ✅ User is redirected back to success page
- ✅ NFT transfer is initiated automatically after payment

**Technical Notes:**
- API: `POST /api/v1/collaborations/payments`
- Creates `payment_records` with Stripe session
- Webhook: `POST /api/v1/collaborations/payments/webhook`
- Updates `orders.status` to 'payment_completed'

### US-P003: Complete NOWPayments Crypto Payment
**As a** user who chose crypto payment  
**I want to** complete payment through NOWPayments  
**So that** I can purchase my selected NFT with cryptocurrency

**Acceptance Criteria:**
- ✅ User is redirected to NOWPayments interface
- ✅ User can select from supported cryptocurrencies
- ✅ Payment auto-converts to target currency (USDT)
- ✅ User has 20 minutes to complete payment
- ✅ User receives payment confirmation
- ✅ NFT transfer is initiated automatically after confirmation

**Technical Notes:**
- API: `POST /api/v1/collaborations/payments`
- Creates `payment_records` with NOWPayments session
- 20-minute timeout enforced
- Webhook processing for crypto confirmation

### US-P004: Handle Payment Failure
**As a** user whose payment fails  
**I want to** be notified of the failure and given option to retry  
**So that** I can complete my purchase if the failure was temporary

**Acceptance Criteria:**
- ✅ User is notified immediately of payment failure
- ✅ User sees specific failure reason when available
- ✅ User can retry payment with same NFT lock
- ✅ User can choose different payment method for retry
- ✅ NFT remains locked during retry attempts
- ✅ After max retries, NFT is unlocked and user exits

**Technical Notes:**
- API: `POST /api/v1/collaborations/payments/:paymentId/retry`
- Updates `payment_records.status` to 'failed'
- Tracks retry count in `payment_records.retryCount`

### US-P005: Handle Payment Timeout
**As a** user whose payment times out  
**I want to** be notified of the timeout and understand next steps  
**So that** I know why my purchase failed and what I can do

**Acceptance Criteria:**
- ✅ User is notified when payment window expires
- ✅ Payment session is automatically cancelled
- ✅ NFT lock is released for other users
- ✅ Order is marked as expired
- ✅ User can rejoin queue to try again

**Technical Notes:**
- Job Service monitors payment timeouts
- Updates `orders.status` to 'expired'
- Releases NFT lock automatically

## 4. Order Management Stories

### US-O001: Track Order Status
**As a** user with an active order  
**I want to** see real-time status of my order progress  
**So that** I know where I am in the purchase and transfer process

**Acceptance Criteria:**
- ✅ User sees current order status (created, payment_pending, etc.)
- ✅ User sees timeline of completed steps
- ✅ User sees estimated time for remaining steps
- ✅ Status updates automatically as order progresses
- ✅ User can access order details anytime via order ID
- ✅ User receives notifications for major status changes

**Technical Notes:**
- API: `GET /api/v1/collaborations/orders/:orderId`
- Real-time status from `orders.status`
- Timeline from timestamp fields

### US-O002: View Order History
**As a** user who has made purchases  
**I want to** see history of all my orders and their status  
**So that** I can track my NFT purchases and transfers

**Acceptance Criteria:**
- ✅ User sees list of all orders for their wallet
- ✅ Each order shows NFT, status, date, and transaction details
- ✅ User can click to see detailed order information
- ✅ User can see NFT transfer transaction hashes
- ✅ User can download order receipts/confirmations
- ✅ History is paginated for performance

**Technical Notes:**
- API: `GET /api/v1/collaborations/orders/wallet/:walletAddress`
- Queries `orders` collection by wallet address
- Includes related `payment_records` and `nft_inventory`



## 5. NFT Transfer Stories

### US-T001: Automatic NFT Transfer After Payment
**As a** user who completed payment  
**I want to** receive my NFT automatically in my wallet  
**So that** I own the NFT I purchased without manual intervention

**Acceptance Criteria:**
- ✅ NFT transfer initiates automatically after payment confirmation
- ✅ Transfer uses Admin Wallet smart contract
- ✅ User receives NFT in their connected wallet address
- ✅ Transfer transaction is recorded on blockchain
- ✅ User receives confirmation with transaction hash
- ✅ Order status updates to 'nft_transferred'

**Technical Notes:**
- Triggered by payment webhook processing
- Uses Admin Wallet contract integration
- Updates `orders.transferTransactionHash`
- Updates `orders.status` to 'nft_transferred'

### US-T002: Handle NFT Transfer Failure
**As a** user whose NFT transfer fails  
**I want to** be notified of the failure and have automatic retry  
**So that** I receive my NFT even if there are temporary blockchain issues

**Acceptance Criteria:**
- ✅ User is notified if NFT transfer fails
- ✅ System automatically retries transfer up to max attempts
- ✅ User sees current retry status and attempt count
- ✅ If all retries fail, admin is notified for manual intervention
- ✅ User receives updates on retry attempts
- ✅ Transfer eventually succeeds or escalates to admin

**Technical Notes:**
- Job Service handles retry logic
- Updates `orders.transferRetryCount`
- Creates `admin_actions` record for manual intervention
- Monitors `orders.transferErrorMessage`

### US-T003: Verify NFT Transfer Completion
**As a** user who should have received an NFT  
**I want to** verify that the transfer completed successfully  
**So that** I can confirm I own the NFT in my wallet

**Acceptance Criteria:**
- ✅ User can verify NFT appears in their wallet
- ✅ System provides blockchain transaction hash for verification
- ✅ User can view NFT on blockchain explorers
- ✅ Order shows final 'nft_transferred' status
- ✅ User receives final confirmation email/notification
- ✅ NFT metadata is properly associated with user's wallet

**Technical Notes:**
- Transaction hash from `orders.transferTransactionHash`
- Final status verification
- Blockchain confirmation monitoring

## 6. Administrative Stories

### US-A001: Configure Sale Parameters
**As an** admin  
**I want to** configure sale timing, queue limits, and timeouts  
**So that** I can control the NFT sale parameters and optimize user experience

**Acceptance Criteria:**
- ✅ Admin can set sale start and end times
- ✅ Admin can configure queue size (max 100) and active pool size
- ✅ Admin can set selection and payment timeouts
- ✅ Admin can enable/disable payment methods
- ✅ Admin can pause/resume sale if needed
- ✅ Configuration changes take effect immediately

**Technical Notes:**
- Updates `sale_config` collection
- Creates `admin_actions` audit record
- Real-time configuration loading by services

### US-A002: Monitor Sale Progress and Timeout Metrics
**As an** admin
**I want to** monitor real-time sale metrics, user activity, and timeout statistics
**So that** I can ensure the sale is running smoothly and optimize timeout settings

**Acceptance Criteria:**
- ✅ Admin sees current queue length and active pool size
- ✅ Admin sees payment success/failure rates
- ✅ Admin sees NFT transfer success rates
- ✅ Admin sees average processing times
- ✅ Admin receives alerts for system issues
- ✅ Admin can view detailed logs and error messages
- ✅ Admin sees timeout statistics by type (waiting, active, payment, cleanup)
- ✅ Admin sees heartbeat timeout removal rates and patterns
- ✅ Admin sees queue position recalculation frequency and performance
- ✅ Admin can monitor processing flow efficiency and timing
- ✅ Admin receives alerts for unusual timeout patterns or high removal rates

**Technical Notes:**
- Aggregated data from all collections including timeout metrics
- Real-time metrics dashboard with timeout constant monitoring
- Integration with monitoring systems
- Timeout statistics from Pool Processor service logs

### US-A003: Handle Manual Interventions
**As an** admin  
**I want to** manually intervene in failed orders or transfers  
**So that** I can resolve issues that require human attention

**Acceptance Criteria:**
- ✅ Admin can manually trigger NFT transfers for failed orders
- ✅ Admin can cancel stuck orders and release NFT locks
- ✅ Admin can reset user queue positions if needed
- ✅ Admin can process refunds for failed purchases
- ✅ All manual actions are logged for audit trail
- ✅ Users are notified of manual interventions

**Technical Notes:**
- Creates detailed `admin_actions` records
- Updates affected `orders` and `nft_inventory`
- Notification system for user communication

This comprehensive set of user stories covers the complete NFT sale workflow with all edge cases and error scenarios based on our System Architecture v2 and Implementation Plan v2.
