import './instrument';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    cors: { origin: '*' },
  });
  app.useGlobalPipes(
    new ValidationPipe({
      // disableErrorMessages: true,
      whitelist: true,
      skipMissingProperties: false,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Swagger API Documentation Setup
  const config = new DocumentBuilder()
    .setTitle('DCasks NFT Sale API')
    .setDescription('Sailing Whisky NFT Sale System API Documentation')
    .setVersion('1.0')
    .addBearerAuth({
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    })
    .addTag('Queue Management', 'Queue and session management endpoints')
    .addTag('Order Management', 'Order creation and tracking endpoints')
    .addTag('NFT Management', 'NFT availability and locking endpoints')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/v1/docs', app, document);

  const configService: ConfigService = app.get(ConfigService);
  const port = configService.get<string>('PORT') || 8000;
  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
  console.log(`Swagger available at: ${await app.getUrl()}/api/v1/docs`);
}
bootstrap();
