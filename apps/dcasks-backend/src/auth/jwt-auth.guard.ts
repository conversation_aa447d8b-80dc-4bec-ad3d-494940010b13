import {
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { GqlExecutionContext } from '@nestjs/graphql';

import { SYSTEM_ROLES } from 'src/common/enum';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private logger = new Logger(JwtAuthGuard.name);

  getRequest(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context);
    return ctx.getContext().req;
  }

  handleRequest<TUser = any>(
    err: any,
    user: any,
    info: any,
    context: ExecutionContext,
    status?: any,
  ): TUser {
    this.logger.debug(`handleRequest > user`, user);
    console.log('TEST', user);
    if (
      ![
        SYSTEM_ROLES.USER_ROLE,
        SYSTEM_ROLES.CASK_OWNER_ROLE,
        SYSTEM_ROLES.ADMIN_ROLE,
        SYSTEM_ROLES.ROOT_ROLE,
      ].includes(user.role)
    ) {
      this.logger.warn(`handleRequest > Invalid role`, {
        user,
        role: user.role,
      });
      throw new ForbiddenException();
    }
    return super.handleRequest(err, user, info, context, status);
  }
}

@Injectable()
export class JwtAuthCaskOwnerGuard extends AuthGuard('jwt') {
  getRequest(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context);
    return ctx.getContext().req;
  }

  handleRequest<TUser = any>(
    err: any,
    user: any,
    info: any,
    context: ExecutionContext,
    status?: any,
  ): TUser {
    if (
      ![
        SYSTEM_ROLES.CASK_OWNER_ROLE,
        SYSTEM_ROLES.ADMIN_ROLE,
        SYSTEM_ROLES.ROOT_ROLE,
      ].includes(user.role)
    ) {
      throw new ForbiddenException();
    }
    return super.handleRequest(err, user, info, context, status);
  }
}

@Injectable()
export class JwtAuthAdminGuard extends AuthGuard('jwt') {
  getRequest(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context);
    return ctx.getContext().req;
  }

  handleRequest<TUser = any>(
    err: any,
    user: any,
    info: any,
    context: ExecutionContext,
    status?: any,
  ): TUser {
    if (
      ![SYSTEM_ROLES.ADMIN_ROLE, SYSTEM_ROLES.ROOT_ROLE].includes(user.role)
    ) {
      throw new ForbiddenException();
    }
    return super.handleRequest(err, user, info, context, status);
  }
}
