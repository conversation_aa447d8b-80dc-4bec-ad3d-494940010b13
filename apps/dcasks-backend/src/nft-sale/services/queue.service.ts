import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import {
  HeartbeatDto,
  HeartbeatResponseDto,
  JoinQueueDto,
  QueueEntryResponseDto,
  QueueStatusResponseDto,
} from '../dto';
import {
  ActivePoolTracking,
  ActivePoolTrackingDocument,
  QueueSession,
  QueueSessionDocument,
  QueueSessionStatus,
  SaleConfig,
  SaleConfigDocument,
} from '../models';

@Injectable()
export class QueueService {
  constructor(
    @InjectModel(QueueSession.name)
    private queueSessionModel: Model<QueueSessionDocument>,
    @InjectModel(ActivePoolTracking.name)
    private activePoolTrackingModel: Model<ActivePoolTrackingDocument>,
    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
  ) {}

  /**
   * Add user to queue or return existing position
   */
  async joinQueue(
    walletAddress: string,
    joinQueueDto: JoinQueueDto,
  ): Promise<QueueEntryResponseDto> {
    const { userAgent, ipAddress } = joinQueueDto;

    // Get current sale configuration
    const saleConfig = await this.getSaleConfig();
    if (!saleConfig.isActive) {
      throw new BadRequestException('Sale is not currently active');
    }
    if (saleConfig.isPaused) {
      throw new BadRequestException(
        `Sale is paused: ${saleConfig.pauseReason || 'Temporarily unavailable'}`,
      );
    }

    // Check if user already in queue
    const existingSession = await this.queueSessionModel.findOne({
      walletAddress,
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.SELECTING_NFT,
        ],
      },
    });

    if (existingSession) {
      // Update heartbeat and return current position
      existingSession.queueHeartbeat = new Date();
      existingSession.connectionCount += 1;
      if (userAgent) existingSession.userAgent = userAgent;
      if (ipAddress) existingSession.ipAddress = ipAddress;
      await existingSession.save();

      const position = await this.calculateQueuePosition(walletAddress);
      const estimatedWaitTime = await this.calculateEstimatedWaitTime(position);

      return {
        position,
        estimatedWaitTime,
        sessionId: existingSession.sessionToken,
        queueSessionId: existingSession._id.toString(),
        status: existingSession.status,
        maxQueueSize: saleConfig.maxQueueSize,
      };
    }

    // Check queue capacity
    const currentQueueSize = await this.queueSessionModel.countDocuments({
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.SELECTING_NFT,
        ],
      },
    });

    if (currentQueueSize >= saleConfig.maxQueueSize) {
      throw new ConflictException('Queue is full. Please try again later.');
    }

    // Create new queue session
    const sessionToken = uuidv4();
    const newSession = new this.queueSessionModel({
      walletAddress,
      position: currentQueueSize + 1,
      status: QueueSessionStatus.WAITING,
      queueJoinedAt: new Date(),
      sessionToken,
      queueHeartbeat: new Date(),
      connectionCount: 1,
      userAgent,
      ipAddress,
    });

    await newSession.save();

    const position = await this.calculateQueuePosition(walletAddress);
    const estimatedWaitTime = await this.calculateEstimatedWaitTime(position);

    return {
      position,
      estimatedWaitTime,
      sessionId: sessionToken,
      queueSessionId: newSession._id.toString(),
      status: newSession.status,
      maxQueueSize: saleConfig.maxQueueSize,
    };
  }

  /**
   * Get current queue status for a user
   */
  async getQueueStatus(walletAddress: string): Promise<QueueStatusResponseDto> {
    const session = await this.queueSessionModel.findOne({
      walletAddress,
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.SELECTING_NFT,
        ],
      },
    });

    if (!session) {
      throw new NotFoundException(
        'No active queue session found for this wallet',
      );
    }

    const position = await this.calculateQueuePosition(walletAddress);
    const estimatedWaitTime =
      position > 0 ? await this.calculateEstimatedWaitTime(position) : 0;

    return {
      position,
      status: session.status,
      poolId: session.poolId,
      canSelectNFT:
        session.status === QueueSessionStatus.ACTIVE ||
        session.status === QueueSessionStatus.SELECTING_NFT,
      estimatedWaitTime: estimatedWaitTime > 0 ? estimatedWaitTime : undefined,
      selectionExpiresAt: session.selectionExpiresAt,
      currentOrderId: session.currentOrderId,
      queueHeartbeat: session.queueHeartbeat,
    };
  }

  /**
   * Process heartbeat to keep session alive
   */
  async processHeartbeat(
    walletAddress: string,
    heartbeatDto: HeartbeatDto,
  ): Promise<HeartbeatResponseDto> {
    const { sessionToken } = heartbeatDto;

    const session = await this.queueSessionModel.findOne({
      walletAddress,
      sessionToken,
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.SELECTING_NFT,
        ],
      },
    });

    if (!session) {
      throw new NotFoundException('Invalid session or session expired');
    }

    // Update heartbeat
    session.queueHeartbeat = new Date();
    await session.save();

    const position = await this.calculateQueuePosition(walletAddress);
    const nextHeartbeat = new Date(Date.now() + 30000); // 30 seconds from now

    return {
      success: true,
      position,
      status: session.status,
      nextHeartbeat,
    };
  }

  /**
   * Calculate current queue position for a wallet
   */
  private async calculateQueuePosition(walletAddress: string): Promise<number> {
    const session = await this.queueSessionModel.findOne({ walletAddress });
    if (!session) return 0;

    if (
      session.status === QueueSessionStatus.ACTIVE ||
      session.status === QueueSessionStatus.SELECTING_NFT
    ) {
      return 0; // User is in active pool
    }

    // Count users ahead in queue
    const usersAhead = await this.queueSessionModel.countDocuments({
      queueJoinedAt: { $lt: session.queueJoinedAt },
      status: QueueSessionStatus.WAITING,
    });

    return usersAhead + 1;
  }

  /**
   * Calculate estimated wait time based on position
   */
  private async calculateEstimatedWaitTime(position: number): Promise<number> {
    if (position <= 0) return 0;

    const saleConfig = await this.getSaleConfig();
    const avgSelectionTime = 5; // 5 minutes average selection time
    const avgPaymentTime = 5; // 5 minutes average payment time
    const totalTimePerUser = avgSelectionTime + avgPaymentTime;

    // Estimate based on pool size and processing time
    const poolSize = saleConfig.maxActivePoolSize;
    const batchesAhead = Math.ceil(position / poolSize);

    return batchesAhead * totalTimePerUser;
  }

  /**
   * Get current sale configuration
   */
  private async getSaleConfig(): Promise<SaleConfigDocument> {
    const config = await this.saleConfigModel
      .findOne({ isActive: true })
      .sort({ createdAt: -1 });
    if (!config) {
      throw new NotFoundException('No active sale configuration found');
    }
    return config;
  }

  /**
   * Remove user from queue (when they leave or complete purchase)
   */
  async leaveQueue(walletAddress: string): Promise<void> {
    await this.queueSessionModel.updateOne(
      { walletAddress, status: { $ne: QueueSessionStatus.COMPLETED } },
      {
        status: QueueSessionStatus.LEFT_QUEUE,
        $unset: { poolId: 1, selectionExpiresAt: 1 },
      },
    );
  }

  /**
   * Move user to active pool (called by pool processor)
   */
  async moveToActivePool(walletAddress: string, poolId: string): Promise<void> {
    const saleConfig = await this.getSaleConfig();
    const selectionExpiresAt = new Date(
      Date.now() + saleConfig.selectionTimeoutMinutes * 60 * 1000,
    );

    await this.queueSessionModel.updateOne(
      { walletAddress, status: QueueSessionStatus.WAITING },
      {
        status: QueueSessionStatus.ACTIVE,
        poolId,
        selectionExpiresAt,
      },
    );
  }

  /**
   * Mark user as selecting NFT
   */
  async markAsSelectingNFT(
    walletAddress: string,
    orderId: string,
  ): Promise<void> {
    await this.queueSessionModel.updateOne(
      { walletAddress, status: QueueSessionStatus.ACTIVE },
      {
        status: QueueSessionStatus.SELECTING_NFT,
        selectionStartedAt: new Date(),
        currentOrderId: orderId,
      },
    );
  }

  /**
   * Complete queue session (when purchase is successful)
   */
  async completeSession(walletAddress: string): Promise<void> {
    await this.queueSessionModel.updateOne(
      { walletAddress },
      { status: QueueSessionStatus.COMPLETED },
    );
  }
}
