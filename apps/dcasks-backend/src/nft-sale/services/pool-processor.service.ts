import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import {
  QueueSession,
  QueueSessionDocument,
  QueueSessionStatus,
  ActivePoolTracking,
  ActivePoolTrackingDocument,
  PoolStatus,
  SaleConfig,
  SaleConfigDocument,
  Order,
  OrderDocument,
  OrderStatus,
} from '../models';

@Injectable()
export class PoolProcessorService {
  private readonly logger = new Logger(PoolProcessorService.name);

  constructor(
    @InjectModel(QueueSession.name)
    private queueSessionModel: Model<QueueSessionDocument>,
    @InjectModel(ActivePoolTracking.name)
    private activePoolTrackingModel: Model<ActivePoolTrackingDocument>,
    @InjectModel(SaleConfig.name)
    private saleConfigModel: Model<SaleConfigDocument>,
    @InjectModel(Order.name)
    private orderModel: Model<OrderDocument>,
  ) {}

  /**
   * Main pool processing job
   */
  async processPool(): Promise<void> {
    try {
      const saleConfig = await this.getSaleConfig();
      if (!saleConfig || !saleConfig.isActive || saleConfig.isPaused) {
        return; // Sale not active, skip processing
      }

      await this.processActivePool(saleConfig);
      await this.fillPoolFromQueue(saleConfig);
      await this.cleanupExpiredSessions();
    } catch (error) {
      this.logger.error('Error in pool processing:', error);
    }
  }

  /**
   * Process active pool - check for timeouts and completions
   */
  private async processActivePool(
    saleConfig: SaleConfigDocument,
  ): Promise<void> {
    const activePool = await this.getOrCreateActivePool(saleConfig);

    // Get all active sessions in the pool
    const activeSessions = await this.queueSessionModel.find({
      poolId: activePool.poolId,
      status: {
        $in: [QueueSessionStatus.ACTIVE, QueueSessionStatus.SELECTING_NFT],
      },
    });

    let poolUpdated = false;
    const currentTime = new Date();

    for (const session of activeSessions) {
      let shouldRemoveFromPool = false;

      // Check for selection timeout
      if (
        session.selectionExpiresAt &&
        session.selectionExpiresAt < currentTime
      ) {
        this.logger.log(`Session ${session.walletAddress} selection timeout`);

        // Expire any active orders
        await this.expireUserOrders(session.walletAddress);

        // Mark session as expired
        await this.queueSessionModel.updateOne(
          { _id: session._id },
          {
            status: QueueSessionStatus.EXPIRED,
            $unset: { poolId: 1, selectionExpiresAt: 1, currentOrderId: 1 },
          },
        );

        shouldRemoveFromPool = true;
      }

      // Check for heartbeat timeout (5 minutes without heartbeat)
      const heartbeatTimeout = new Date(currentTime.getTime() - 5 * 60 * 1000);
      if (session.queueHeartbeat && session.queueHeartbeat < heartbeatTimeout) {
        this.logger.log(`Session ${session.walletAddress} heartbeat timeout`);

        await this.queueSessionModel.updateOne(
          { _id: session._id },
          {
            status: QueueSessionStatus.EXPIRED,
            $unset: { poolId: 1, selectionExpiresAt: 1, currentOrderId: 1 },
          },
        );

        shouldRemoveFromPool = true;
      }

      // Check if user completed purchase
      if (session.status === QueueSessionStatus.COMPLETED) {
        shouldRemoveFromPool = true;
      }

      if (shouldRemoveFromPool) {
        // Remove from active users array
        await this.activePoolTrackingModel.updateOne(
          { poolId: activePool.poolId },
          {
            $pull: { activeUsers: session.walletAddress },
            $inc: { currentPoolSize: -1 },
          },
        );
        poolUpdated = true;
      }
    }

    if (poolUpdated) {
      // Update pool statistics
      await this.updatePoolStatistics(activePool.poolId);
    }
  }

  /**
   * Fill pool from queue if there's space
   */
  private async fillPoolFromQueue(
    saleConfig: SaleConfigDocument,
  ): Promise<void> {
    const activePool = await this.getOrCreateActivePool(saleConfig);

    // Check how many spots are available
    const availableSpots =
      saleConfig.maxActivePoolSize - activePool.currentPoolSize;

    if (availableSpots <= 0) {
      return; // Pool is full
    }

    // Get waiting users from queue (oldest first)
    const waitingUsers = await this.queueSessionModel
      .find({ status: QueueSessionStatus.WAITING })
      .sort({ queueJoinedAt: 1 })
      .limit(availableSpots);

    if (waitingUsers.length === 0) {
      return; // No users waiting
    }

    this.logger.log(`Moving ${waitingUsers.length} users to active pool`);

    const selectionExpiresAt = new Date(
      Date.now() + saleConfig.selectionTimeoutMinutes * 60 * 1000,
    );

    for (const user of waitingUsers) {
      // Move user to active pool
      await this.queueSessionModel.updateOne(
        { _id: user._id },
        {
          status: QueueSessionStatus.ACTIVE,
          poolId: activePool.poolId,
          selectionExpiresAt,
        },
      );

      // Add to active users array
      await this.activePoolTrackingModel.updateOne(
        { poolId: activePool.poolId },
        {
          $addToSet: { activeUsers: user.walletAddress },
          $inc: { currentPoolSize: 1, totalProcessed: 1 },
        },
      );
    }

    await this.updatePoolStatistics(activePool.poolId);
  }

  /**
   * Clean up expired sessions and orders
   */
  private async cleanupExpiredSessions(): Promise<void> {
    const currentTime = new Date();

    // Find expired sessions
    const expiredSessions = await this.queueSessionModel.find({
      status: {
        $in: [
          QueueSessionStatus.WAITING,
          QueueSessionStatus.ACTIVE,
          QueueSessionStatus.SELECTING_NFT,
        ],
      },
      $or: [
        { selectionExpiresAt: { $lt: currentTime } },
        {
          queueHeartbeat: {
            $lt: new Date(currentTime.getTime() - 10 * 60 * 1000),
          },
        }, // 10 minutes
      ],
    });

    for (const session of expiredSessions) {
      this.logger.log(`Cleaning up expired session: ${session.walletAddress}`);

      // Expire any active orders
      await this.expireUserOrders(session.walletAddress);

      // Update session status
      await this.queueSessionModel.updateOne(
        { _id: session._id },
        {
          status: QueueSessionStatus.EXPIRED,
          $unset: { poolId: 1, selectionExpiresAt: 1, currentOrderId: 1 },
        },
      );
    }
  }

  /**
   * Get or create active pool
   */
  private async getOrCreateActivePool(
    saleConfig: SaleConfigDocument,
  ): Promise<ActivePoolTrackingDocument> {
    let activePool = await this.activePoolTrackingModel.findOne({
      status: PoolStatus.ACTIVE,
    });

    if (!activePool) {
      // Create new active pool
      const poolId = `pool_${uuidv4().replace(/-/g, '')}`;

      activePool = new this.activePoolTrackingModel({
        poolId,
        maxPoolSize: saleConfig.maxActivePoolSize,
        currentPoolSize: 0,
        status: PoolStatus.ACTIVE,
        activeUsers: [],
        totalProcessed: 0,
        createdAt: new Date(),
        lastUpdatedAt: new Date(),
        successfulPurchases: 0,
        failedPurchases: 0,
        timeoutCount: 0,
        averageSelectionTime: 0,
        averagePaymentTime: 0,
        saleConfigVersion: saleConfig.configVersion,
      });

      await activePool.save();
      this.logger.log(`Created new active pool: ${poolId}`);
    }

    return activePool;
  }

  /**
   * Update pool statistics
   */
  private async updatePoolStatistics(poolId: string): Promise<void> {
    const currentPoolSize = await this.queueSessionModel.countDocuments({
      poolId,
      status: {
        $in: [QueueSessionStatus.ACTIVE, QueueSessionStatus.SELECTING_NFT],
      },
    });

    await this.activePoolTrackingModel.updateOne(
      { poolId },
      {
        currentPoolSize,
        lastUpdatedAt: new Date(),
      },
    );
  }

  /**
   * Expire user orders
   */
  private async expireUserOrders(walletAddress: string): Promise<void> {
    await this.orderModel.updateMany(
      {
        walletAddress,
        status: {
          $in: [
            OrderStatus.CREATED,
            OrderStatus.NFT_LOCKED,
            OrderStatus.PAYMENT_PENDING,
          ],
        },
      },
      { status: OrderStatus.EXPIRED },
    );
  }

  /**
   * Get current sale configuration
   */
  private async getSaleConfig(): Promise<SaleConfigDocument | null> {
    return await this.saleConfigModel
      .findOne({ isActive: true })
      .sort({ createdAt: -1 });
  }

  /**
   * Manual method to reset pool (for admin use)
   */
  async resetPool(): Promise<void> {
    this.logger.log('Manually resetting pool');

    // Mark current pool as closed
    await this.activePoolTrackingModel.updateMany(
      { status: PoolStatus.ACTIVE },
      { status: PoolStatus.CLOSED },
    );

    // Reset all active sessions to waiting
    await this.queueSessionModel.updateMany(
      {
        status: {
          $in: [QueueSessionStatus.ACTIVE, QueueSessionStatus.SELECTING_NFT],
        },
      },
      {
        status: QueueSessionStatus.WAITING,
        $unset: {
          poolId: 1,
          selectionExpiresAt: 1,
          selectionStartedAt: 1,
        },
      },
    );

    this.logger.log('Pool reset completed');
  }
}
