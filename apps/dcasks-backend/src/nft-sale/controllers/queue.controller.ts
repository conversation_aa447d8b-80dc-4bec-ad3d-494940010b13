import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';

import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { User } from '../../auth/auth.decorator';
import { UserPayload } from '../../auth/type/user-payload.type';
import { QueueService } from '../services';
import {
  JoinQueueDto,
  QueueStatusQueryDto,
  QueueEntryResponseDto,
  QueueStatusResponseDto,
  HeartbeatDto,
  HeartbeatResponseDto,
} from '../dto';

@ApiTags('Queue Management')
@Controller('api/v1/collaborations')
export class QueueController {
  constructor(private readonly queueService: QueueService) {}

  @Post('queue')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Join NFT sale queue',
    description: 'Add user to the NFT sale queue using JWT authentication',
  })
  @ApiBody({ type: JoinQueueDto })
  @ApiResponse({
    status: 200,
    description: 'Successfully joined the queue',
    type: QueueEntryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid JWT token',
  })
  @ApiResponse({
    status: 409,
    description: 'User already in queue',
  })
  async joinQueue(
    @Body() joinQueueDto: JoinQueueDto,
    @User() user: UserPayload,
    @Req() request: Request,
  ): Promise<QueueEntryResponseDto> {
    // Extract additional metadata from request
    const userAgent = request.headers['user-agent'];
    const ipAddress = request.ip || request.socket.remoteAddress;

    const enrichedDto = {
      ...joinQueueDto,
      userAgent,
      ipAddress,
    };

    console.log(`joinQueue > user`, user);
    console.log(this);

    // Use wallet address from JWT token
    return await this.queueService.joinQueue(user.publicAddress, enrichedDto);
  }

  @Get('queue/status')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Get queue status',
    description:
      'Retrieve current queue position and status for authenticated user',
  })
  @ApiResponse({
    status: 200,
    description: 'Queue status retrieved successfully',
    type: QueueStatusResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid JWT token',
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet address not found in queue',
  })
  async getQueueStatus(
    @User() user: UserPayload,
  ): Promise<QueueStatusResponseDto> {
    return await this.queueService.getQueueStatus(user.publicAddress);
  }

  @Post('queue/heartbeat')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Send queue heartbeat',
    description:
      'Keep queue session alive by sending periodic heartbeat signals',
  })
  @ApiBody({ type: HeartbeatDto })
  @ApiResponse({
    status: 200,
    description: 'Heartbeat processed successfully',
    type: HeartbeatResponseDto,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid JWT token',
  })
  @ApiResponse({
    status: 404,
    description: 'Queue session not found',
  })
  async processHeartbeat(
    @Body() heartbeatDto: HeartbeatDto,
    @User() user: UserPayload,
  ): Promise<HeartbeatResponseDto> {
    return await this.queueService.processHeartbeat(
      user.publicAddress,
      heartbeatDto,
    );
  }

  @Post('queue/leave')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Leave queue',
    description: 'Remove authenticated user from the NFT sale queue',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully left the queue',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Successfully left the queue' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid JWT token',
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet address not found in queue',
  })
  async leaveQueue(
    @User() user: UserPayload,
  ): Promise<{ success: boolean; message: string }> {
    await this.queueService.leaveQueue(user.publicAddress);
    return {
      success: true,
      message: 'Successfully left the queue',
    };
  }
}
