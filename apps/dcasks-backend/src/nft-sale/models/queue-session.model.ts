import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import GraphQLJSON from 'graphql-type-json';

export enum QueueSessionStatus {
  WAITING = 'WAITING',
  ACTIVE = 'ACTIVE',
  PAYMENT = 'PAYMENT',
  COMPLETED = 'COMPLETED',
  EXPIRED = 'EXPIRED',
  LEFT_QUEUE = 'LEFT_QUEUE',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'queue_sessions' })
export class QueueSession {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  walletAddress: string;

  @Field()
  @Prop({ type: Number, required: true, index: true })
  position: number; // Queue position - used for FIFO ordering

  @Field(() => QueueSessionStatus)
  @Prop({
    type: String,
    required: true,
    enum: QueueSessionStatus,
    index: true,
    default: QueueSessionStatus.WAITING,
  })
  status: QueueSessionStatus;

  @Field({ nullable: true })
  @Prop({ type: String, index: true })
  poolId?: string;

  // === QUEUE-RELATED TIME FIELDS ===
  @Field()
  @Prop({ type: Date, required: true })
  queueJoinedAt: Date; // Timestamp when user first joined the queue - used for FIFO ordering

  @Field({ nullable: true })
  @Prop({ type: Date })
  queueHeartbeat?: Date; // Timestamp of last heartbeat received from user

  // === ACTIVE SESSION TIME FIELDS ===
  @Field({ nullable: true })
  @Prop({ type: Date })
  queueActivatedAt?: Date; // Timestamp when user was moved from WAITING to ACTIVE status by pool processor

  @Field({ nullable: true })
  @Prop({ type: Date })
  activeSessionExpiresAt?: Date; // Timestamp when active session expires if no order is created

  @Field({ nullable: true })
  @Prop({ type: Date })
  activeHeartbeat?: Date; // Timestamp of last heartbeat during active phase

  // === PAYMENT-RELATED TIME FIELDS ===
  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentStartedAt?: Date; // Timestamp when payment process started

  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentExpiresAt?: Date; // Timestamp when payment window expires (e.g., 20 minutes after starting payment)

  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentCompletedAt?: Date; // Timestamp when payment was confirmed

  // === SESSION MANAGEMENT ===
  @Field({ nullable: true })
  @Prop({ type: String })
  sessionToken?: string;

  @Field({ nullable: true })
  @Prop({ type: Number, default: 0 })
  connectionCount: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  currentOrderId?: string; // Reference to active order

  @Field({ nullable: true })
  @Prop({ type: String })
  userAgent?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  ipAddress?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  sessionData?: Record<string, any>;

  @Field()
  @Prop({ type: Date })
  createdAt: Date;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type QueueSessionDocument = QueueSession & Document;
export const QueueSessionSchema = SchemaFactory.createForClass(QueueSession);

// Additional indexes for performance
QueueSessionSchema.index({ position: 1, status: 1 });
QueueSessionSchema.index({ poolId: 1, status: 1 });
QueueSessionSchema.index({ queueHeartbeat: 1 });
QueueSessionSchema.index({ selectionExpiresAt: 1 });
QueueSessionSchema.index({ paymentExpiresAt: 1 });
