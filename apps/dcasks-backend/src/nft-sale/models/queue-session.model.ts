import { Field, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import GraphQLJSON from 'graphql-type-json';

export enum QueueSessionStatus {
  WAITING = 'WAITING',
  ACTIVE = 'ACTIVE',
  SELECTING_NFT = 'SELECTING_NFT',
  COMPLETED = 'COMPLETED',
  EXPIRED = 'EXPIRED',
  LEFT_QUEUE = 'LEFT_QUEUE',
}

@ObjectType()
@Schema({ timestamps: true, collection: 'queue_sessions' })
export class QueueSession {
  @Field()
  _id: string;

  @Field()
  @Prop({ type: String, required: true, unique: true, index: true })
  walletAddress: string;

  @Field()
  @Prop({ type: Number, required: true, index: true })
  position: number;

  @Field(() => QueueSessionStatus)
  @Prop({
    type: String,
    required: true,
    enum: QueueSessionStatus,
    index: true,
    default: QueueSessionStatus.WAITING,
  })
  status: QueueSessionStatus;

  @Field({ nullable: true })
  @Prop({ type: String, index: true })
  poolId?: string;

  // === QUEUE-RELATED TIME FIELDS ===
  @Field()
  @Prop({ type: Date, required: true })
  queueJoinedAt: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  queueActivatedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  queueLastHeartbeat?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date, index: true })
  queueExpiresAt?: Date;

  // === SELECTION-RELATED TIME FIELDS ===
  @Field({ nullable: true })
  @Prop({ type: Date })
  selectionStartedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  selectionExpiresAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  selectionHeartbeat?: Date;

  // === PAYMENT-RELATED TIME FIELDS ===
  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentStartedAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentExpiresAt?: Date;

  @Field({ nullable: true })
  @Prop({ type: Date })
  paymentCompletedAt?: Date;

  // === SESSION MANAGEMENT ===
  @Field({ nullable: true })
  @Prop({ type: String })
  sessionToken?: string;

  @Field({ nullable: true })
  @Prop({ type: Number, default: 0 })
  connectionCount?: number;

  @Field({ nullable: true })
  @Prop({ type: String })
  currentOrderId?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  userAgent?: string;

  @Field({ nullable: true })
  @Prop({ type: String })
  ipAddress?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  @Prop({ type: Object })
  sessionData?: Record<string, any>;

  @Field()
  @Prop({ type: Date })
  createdAt: Date;

  @Field()
  @Prop({ type: Date })
  updatedAt: Date;
}

export type QueueSessionDocument = QueueSession & Document;
export const QueueSessionSchema = SchemaFactory.createForClass(QueueSession);

// Additional indexes for performance
QueueSessionSchema.index({ position: 1, status: 1 });
QueueSessionSchema.index({ poolId: 1, status: 1 });
QueueSessionSchema.index({ queueExpiresAt: 1 });
QueueSessionSchema.index({ queueLastHeartbeat: 1 });
QueueSessionSchema.index({ selectionExpiresAt: 1 });
QueueSessionSchema.index({ paymentExpiresAt: 1 });
