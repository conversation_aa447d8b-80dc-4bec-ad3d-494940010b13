import { forwardRef, Module, OnApplicationBootstrap } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import schedule from 'node-schedule';

import { BlockchainEventModule } from 'src/blockchain-event/blockchain-event.module';
import { BlockchainEventService } from 'src/blockchain-event/blockchain-event.service';
import {
  CONFIRMED_BLOCK,
  GAP_QUERY_FILTER_EVENTS,
  INTERVAL_CONSUME_AIRDROP,
  INTERVAL_HANDLE_EVENTS,
  INTERVAL_QUERY_FILTER_EVENTS,
} from 'src/config/contants';
import { Brc420Module } from 'src/mining/brc420/brc420.module';
import { Brc420Service } from 'src/mining/brc420/brc420.service';
import { ProviderModule } from 'src/provider/provider.module';
import { sleep } from './utils/time';
import { MetaplexService } from './mining-sol/metaplex.service';
import { MiningSolModule } from './mining-sol/mining-sol.module';
import { AirdropService } from './mining-sol/airdrop.service';
import { MiningService } from './mining-sol/mining.service';
import { APP_FILTER } from '@nestjs/core';
import { SentryGlobalFilter } from '@sentry/nestjs/setup';
import { NFTSaleModule } from './nft-sale/nft-sale.module';
import { PoolProcessorService } from './nft-sale/services/pool-processor.service';
import { PaymentMonitorService } from './nft-sale/services/payment-monitor.service';

type JobSchedule = {
  name: string;
  enable: boolean;
  func: () => Promise<void> | void;
  cronExpression: string;
  timeout?: number;
  errorTimeout?: number;
};

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true, cache: true }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          uri: configService.get<string>('DATABASE_URI'),
        };
      },
    }),
    ProviderModule,
    BlockchainEventModule,
    Brc420Module,
    MiningSolModule,
    NFTSaleModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: SentryGlobalFilter,
    },
  ],
})
export class JobModule implements OnApplicationBootstrap {
  constructor(
    private readonly blockchainEventService: BlockchainEventService,
    // private readonly airdropService: AirdropService,
    private readonly brc420Service: Brc420Service,
    private readonly metaplexService: MetaplexService,
    private readonly airdropService: AirdropService,
    private readonly miningService: MiningService,
    private readonly poolProcessorService: PoolProcessorService,
    private readonly paymentMonitorService: PaymentMonitorService,
  ) {}

  jobs: JobSchedule[] = [
    // {
    //   name: 'CRON-EVENT',
    //   enable: true,
    //   func: this.cronEvent,
    //   cronExpression: '*/5 * * * * *',
    //   timeout: 10,
    //   errorTimeout: 10,
    // },
  ];

  cronStatus: { [key in string]: boolean } = {};

  async onApplicationBootstrap() {
    this.blockchainEventService.startListenEvents();

    (async () => {
      while (true) {
        await this.blockchainEventService.handleBlockchainEvents();
        await this.waitMs(INTERVAL_HANDLE_EVENTS);
      }
    })();

    (async () => {
      while (true) {
        const { start, end, currentBlock } =
          await this.blockchainEventService.startQueryFilterEvents();

        await this.waitMs(GAP_QUERY_FILTER_EVENTS);

        // if reach latest block
        if (end >= currentBlock - CONFIRMED_BLOCK) {
          await this.waitMs(INTERVAL_QUERY_FILTER_EVENTS);
        }
      }
    })();

    // (async () => {
    //   while (true) {
    //     const tokenAirdrop =
    //       await this.airdropService.consumeSendTokenAirdrop();
    //     const emailAirdrop =
    //       await this.airdropService.consumeSendEmailAirdrop();

    //     if (!tokenAirdrop && !emailAirdrop) {
    //       await this.wait(INTERVAL_CONSUME_AIRDROP);
    //     }
    //   }
    // })();

    // (async () => {
    //   while (true) {
    //     await this.brc420Service.startSyncMintedTxs();
    //     await this.waitMs(10 * 60 * 1000); // 10 minutes
    //   }
    // })();

    // (async () => {
    //   await sleep(10000); // 10 seconds
    //   while (true) {
    //     await this.brc420Service.startSyncTradedTxs();
    //     await this.waitMs(10 * 60 * 1000); // 10 minutes
    //   }
    // })();

    // (async () => {
    //   await sleep(30000); // 30 seconds
    //   while (true) {
    //     await this.brc420Service.startSyncTransferTxs();
    //     await this.waitMs(30 * 60 * 1000); // 30 minutes
    //   }
    // })();

    // (async () => {
    //   await sleep(10000); // 10 seconds
    //   while (true) {
    //     await this.metaplexService.syncAllNfts();
    //     await this.waitMs(60 * 1000); // 1 min
    //   }
    // })();

    const rule = new schedule.RecurrenceRule();
    rule.hour = 0;
    rule.minute = 1;
    rule.tz = 'Etc/UTC';

    // schedule.scheduleJob(rule, async () => {
    //   console.log('Creating airdrop v2');
    //   await this.airdropService.createAirdropV2();

    //   console.log('Sending airdrop v2');
    //   await this.airdropService.sendAirdropV2();
    // });

    // schedule.scheduleJob(rule, async () => {
    //   console.log('Creating mining');
    //   await this.miningService.createMining();

    //   console.log('Sending mining');
    //   await this.miningService.sendMining();
    // });

    // NFT Sale Pool Processing - every 30 seconds
    (async () => {
      while (true) {
        try {
          await this.poolProcessorService.processPool();
        } catch (error) {
          console.error('Error in pool processing job:', error);
        }
        await this.waitMs(30000); // 30 seconds
      }
    })();

    // NFT Sale Payment Monitoring - every minute
    (async () => {
      while (true) {
        try {
          await this.paymentMonitorService.monitorPayments();
        } catch (error) {
          console.error('Error in payment monitoring job:', error);
        }
        await this.waitMs(60000); // 1 minute
      }
    })();


  }

  private timeout(s: number) {
    return new Promise((_, rj) => {
      setTimeout(() => rj(new Error('timeout succeeded')), s * 1000);
    });
  }

  private wait(s: number) {
    return new Promise((rs, rj) => {
      setTimeout(() => rs(''), s * 1000);
    });
  }

  private waitMs(ms: number) {
    return new Promise((rs, rj) => {
      setTimeout(() => rs(''), ms);
    });
  }

  // private cronEvent() {}
}
