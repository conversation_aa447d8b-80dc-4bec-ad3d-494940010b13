import { ObjectId } from 'mongodb';

export class TestDataFactory {
  // Generate consistent test wallet addresses
  static generateWalletAddress(index?: number): string {
    if (index !== undefined) {
      return `0x${index.toString(16).padStart(40, '0')}`;
    }
    return `0x${Math.random().toString(16).substr(2, 40)}`;
  }

  // Generate consistent test token IDs
  static generateTokenId(index?: number): string {
    return index !== undefined
      ? `${1000 + index}`
      : Math.floor(Math.random() * 10000).toString();
  }

  // 1. Sale Configuration (Required for all tests)
  static createSaleConfig() {
    return {
      _id: new ObjectId(),
      configVersion: 'v1.0.0-sit',
      saleStartTime: new Date('2024-01-01T00:00:00Z'),
      saleEndTime: new Date('2025-12-31T23:59:59Z'),
      isActive: true,
      isPaused: false,
      pauseReason: null,
      maxQueueSize: 100,
      maxActivePoolSize: 10,
      selectionTimeoutMinutes: 5,
      paymentTimeoutMinutes: 10,
      queueSessionTimeoutMinutes: 30,
      stripeTimeoutMinutes: 15,
      nowpaymentsTimeoutMinutes: 20,
      nftCollectionAddress: '******************************************',
      nftBasePrice: 100,
      nftCurrency: 'USD',
      maxConcurrentPayments: 50,
      maxRetryAttempts: 3,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }

  // 2. Active Pool Tracking (Required for queue management)
  static createActivePoolTracking() {
    return {
      _id: new ObjectId(),
      poolId: 'main-pool-sit',
      maxPoolSize: 10,
      currentPoolSize: 0,
      status: 'ACTIVE',
      totalProcessed: 0,
      createdAt: new Date(),
      lastUpdatedAt: new Date(),
      successfulPurchases: 0,
      failedPurchases: 0,
      timeoutCount: 0,
      averageSelectionTime: null,
      averagePaymentTime: null,
      saleConfigVersion: 'v1.0.0-sit',
      updatedAt: new Date(),
    };
  }

  // 3. NFT Inventory (Required for NFT selection)
  static createNFTInventory(count: number = 50) {
    const nfts = [];

    for (let i = 1; i <= count; i++) {
      nfts.push({
        _id: new ObjectId(),
        tokenId: this.generateTokenId(i),
        collectionAddress: '******************************************',
        name: `Sailing Whisky NFT #${i}`,
        description: `Premium whisky NFT #${i} from the Sailing Collection`,
        imageUrl: `https://example.com/nft-images/${i}.jpg`,
        metadataUrl: `https://example.com/metadata/${i}.json`,
        metadata: {
          rarity:
            i <= 5
              ? 'legendary'
              : i <= 15
                ? 'rare'
                : i <= 30
                  ? 'uncommon'
                  : 'common',
          attributes: [
            {
              trait_type: 'Rarity',
              value:
                i <= 5
                  ? 'Legendary'
                  : i <= 15
                    ? 'Rare'
                    : i <= 30
                      ? 'Uncommon'
                      : 'Common',
            },
            { trait_type: 'Age', value: `${10 + (i % 20)} years` },
            {
              trait_type: 'Region',
              value:
                i % 3 === 0 ? 'Speyside' : i % 3 === 1 ? 'Islay' : 'Highland',
            },
            {
              trait_type: 'Cask Type',
              value: i % 2 === 0 ? 'Bourbon Barrel' : 'Sherry Cask',
            },
          ],
        },
        price: 100 + i * 10, // Varying prices
        currency: 'USD',
        status: 'AVAILABLE',
        lockedBy: null,
        lockedAt: null,
        lockExpiresAt: null,
        orderId: null,
        currentOwner: '******************************************', // Contract address
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return nfts;
  }

  // 4. Sample Queue Sessions (for testing queue functionality)
  static createSampleQueueSessions(count: number = 5) {
    const sessions = [];

    for (let i = 1; i <= count; i++) {
      sessions.push({
        _id: new ObjectId(),
        walletAddress: this.generateWalletAddress(i),
        position: i,
        status: 'WAITING',
        poolId: null,
        queueJoinedAt: new Date(Date.now() - i * 60000), // Staggered join times
        queueActivatedAt: null,
        selectionStartedAt: null,
        selectionExpiresAt: null,
        queueExpiresAt: new Date(Date.now() + 30 * 60000), // 30 minutes from now
        sessionToken: `test-session-token-${i}`,
        queueLastHeartbeat: new Date(),
        connectionCount: 1,
        currentOrderId: null,
        userAgent: 'test-user-agent',
        ipAddress: '127.0.0.1',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return sessions;
  }

  // 5. Sample Orders (for testing order functionality)
  static createSampleOrders(count: number = 3) {
    const orders = [];

    for (let i = 1; i <= count; i++) {
      orders.push({
        _id: new ObjectId(),
        orderId: `ORDER-SIT-${i.toString().padStart(6, '0')}`,
        walletAddress: this.generateWalletAddress(i),
        queueSessionId: new ObjectId(),
        nftTokenId: this.generateTokenId(i),
        status: 'CREATED',
        substatus: null,
        nftPrice: 100 + i * 10,
        currency: 'USD',
        createdAt: new Date(),
        nftLockedAt: new Date(),
        paymentInitiatedAt: null,
        paymentCompletedAt: null,
        nftTransferInitiatedAt: null,
        nftTransferCompletedAt: null,
        expiresAt: new Date(Date.now() + 10 * 60000), // 10 minutes from now
        paymentMethod: i % 2 === 0 ? 'STRIPE' : 'NOWPAYMENTS',
        transferTransactionHash: null,
        transferRetryCount: 0,
        transferErrorMessage: null,
        userAgent: 'test-user-agent',
        ipAddress: '127.0.0.1',
        updatedAt: new Date(),
      });
    }

    return orders;
  }

  // 6. Sample Payment Records
  static createSamplePaymentRecords(count: number = 2) {
    const payments = [];

    for (let i = 1; i <= count; i++) {
      payments.push({
        _id: new ObjectId(),
        paymentId: `PAY-SIT-${i.toString().padStart(6, '0')}`,
        orderId: `ORDER-SIT-${i.toString().padStart(6, '0')}`,
        walletAddress: this.generateWalletAddress(i),
        paymentMethod: i % 2 === 0 ? 'STRIPE' : 'NOWPAYMENTS',
        paymentProvider: i % 2 === 0 ? 'stripe' : 'nowpayments',
        paymentSessionId: `session_${i}_test`,
        amount: 100 + i * 10,
        currency: 'USD',
        originalAmount: null,
        originalCurrency: null,
        exchangeRate: null,
        status: 'CREATED',
        failureReason: null,
        externalTransactionId: null,
        externalSessionUrl: null,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 15 * 60000), // 15 minutes from now
        completedAt: null,
        failedAt: null,
        webhookReceived: false,
        webhookData: null,
        webhookProcessedAt: null,
        webhookRetryCount: 0,
        retryCount: 0,
        lastRetryAt: null,
        userAgent: 'test-user-agent',
        ipAddress: '127.0.0.1',
        updatedAt: new Date(),
      });
    }

    return payments;
  }

  // 7. Sample Admin Actions
  static createSampleAdminActions(count: number = 3) {
    const actions = [];
    const actionTypes = [
      'CONFIG_UPDATE',
      'QUEUE_MANAGEMENT',
      'ORDER_MANAGEMENT',
    ];

    for (let i = 1; i <= count; i++) {
      actions.push({
        _id: new ObjectId(),
        actionType: actionTypes[i % actionTypes.length],
        adminUser: 'test-admin',
        targetEntity: 'SALE',
        targetId: `target-${i}`,
        metadata: {
          action: `Test admin action ${i}`,
          previousValue: `old-value-${i}`,
          newValue: `new-value-${i}`,
        },
        reason: `Test action for SIT environment ${i}`,
        executedAt: new Date(),
        success: true,
        errorMessage: null,
        affectedRecords: 1,
        ipAddress: '127.0.0.1',
        userAgent: 'test-admin-agent',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    return actions;
  }

  // Generate complete test dataset
  static generateCompleteTestData() {
    return {
      sale_config: [this.createSaleConfig()],
      active_pool_tracking: [this.createActivePoolTracking()],
      nft_inventory: this.createNFTInventory(50),
      queue_sessions: this.createSampleQueueSessions(5),
      orders: this.createSampleOrders(3),
      payment_records: this.createSamplePaymentRecords(2),
      admin_actions: this.createSampleAdminActions(3),
    };
  }
}
